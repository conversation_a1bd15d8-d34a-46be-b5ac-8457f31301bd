# ==============================================
# REQUIRED CONFIGURATION
# ==============================================

# API Host URL (required)
# This should be the URL where your Flowise instance is running
# Example: https://your-flowise-instance.com
API_HOST=

# Flowise API Key (required)
# Generate this from your Flowise instance settings page
# Example: OxxGE-h_LaH7ZYorStjTOik1XY999RxxoHpCSYl8BXxc
FLOWISE_API_KEY=

# ==============================================
# CHATFLOWS CONFIGURATION (required)
# ==============================================

# Format: [identifier]=[chatflowId],[allowedDomain1],[allowedDomain2],...
#
# Each entry consists of:
# - identifier: Any name you choose (e.g., agent1, support, salesbot)
# - chatflowId: The UUID of your Flowise chatflow
# - allowedDomains: Comma-separated list of domains where this chat can be embedded
#                   Note: Wildcard domains (*) are not supported for security reasons
#
# Examples:
# agent1=20db97c6-64c9-4411-bab4-7d6202171600,https://example1.com
# support=1c28f529-a70f-5001-9bc5-4f4c5d03d8c0,https://example2.com,https://another-example2.com
# salesbot=3db97c6-64c9-4411-bab4-7d620217160a,https://sales.example.com

# Add your chatflows below:
chatflow_1=
chatflow_2=
