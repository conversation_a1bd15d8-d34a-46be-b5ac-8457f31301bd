import { Show } from 'solid-js';
import { Portal } from 'solid-js/web';

export const PreviewImage = (props: { previewImg: () => Element | null; setPreviewImg: Function }) => {
  return (
    <Portal>
      <Show when={props.previewImg()}>
        {(el) => (
          <div
            id="preview-img-modal"
            style="
            position: fixed;
            top: 0; left: 0;
            width: 100vw; height: 100vh;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999999999999999999;
          "
            onClick={() => {
              props.setPreviewImg(null);
            }}
          >
            {el()}
          </div>
        )}
      </Show>
    </Portal>
  );
};
