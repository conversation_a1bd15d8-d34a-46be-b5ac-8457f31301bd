import { ShortTextInput } from './ShortTextInput';
import { isMobile } from '@/utils/isMobileSignal';
import { Show, createSignal, createEffect, onMount, Setter, For } from 'solid-js';
import { SendButton } from '@/components/buttons/SendButton';
import { FileEvent, IAction, UploadsConfig } from '@/components/Bot';
import { ImageUploadButton } from '@/components/buttons/ImageUploadButton';
import { RecordAudioButton } from '@/components/buttons/RecordAudioButton';
import { AttachmentUploadButton } from '@/components/buttons/AttachmentUploadButton';
import { ChatInputHistory } from '@/utils/chatInputHistory';
import { searchFaqsQuery, vectorSearchFaqsQuery } from '@/queries/sendMessageQuery';
import { debounce } from 'lodash';

type TextInputProps = {
  placeholder?: string;
  backgroundColor?: string;
  textColor?: string;
  sendButtonColor?: string;
  inputValue: string;
  fontSize?: number;
  disabled?: boolean;
  onSubmit: (value: string, action?: IAction | undefined | null, faqId?: string) => void;
  onInputChange: (value: string) => void;
  uploadsConfig?: Partial<UploadsConfig>;
  isFullFileUpload?: boolean;
  setPreviews: Setter<unknown[]>;
  onMicrophoneClicked: () => void;
  handleFileChange: (event: FileEvent<HTMLInputElement>) => void;
  maxChars?: number;
  maxCharsWarningMessage?: string;
  autoFocus?: boolean;
  sendMessageSound?: boolean;
  sendSoundLocation?: string;
  enableInputHistory?: boolean;
  maxHistorySize?: number;
  isLoading?: boolean;
  apiHost?: string;
  chatflowid: string;
  isUseFAQ?: boolean;
  setDisableInput: Setter<boolean>;
};

const defaultBackgroundColor = '#ffffff';
const defaultTextColor = '#303235';
// CDN link for default send sound
const defaultSendSound = 'https://cdn.jsdelivr.net/gh/FlowiseAI/FlowiseChatEmbed@latest/src/assets/send_message.mp3';

export const TextInput = (props: TextInputProps) => {
  const [isSendButtonDisabled, setIsSendButtonDisabled] = createSignal(false);
  const [warningMessage, setWarningMessage] = createSignal('');
  const [inputHistory] = createSignal(new ChatInputHistory(() => props.maxHistorySize || 10));
  const [showSuggestions, setShowSuggestions] = createSignal(false);
  const [filteredSuggestions, setFilteredSuggestions] = createSignal<any[]>([]);
  const [currentInputValue, setCurrentInputValue] = createSignal(props.inputValue); // Track current input value locally
  const [skipNextSearch, setSkipNextSearch] = createSignal(false); // Added flag to control search execution

  let inputRef: HTMLInputElement | HTMLTextAreaElement | undefined;
  let fileUploadRef: HTMLInputElement | HTMLTextAreaElement | undefined;
  let imgUploadRef: HTMLInputElement | HTMLTextAreaElement | undefined;
  let audioRef: HTMLAudioElement | undefined;
  let suggestionsRef: HTMLDivElement | undefined;

  // Create a debounced version of handleInput
  const debouncedHandleInput = debounce(async (inputValue: string) => {
    const wordCount = inputValue.length;

    if (props.maxChars && wordCount > props.maxChars) {
      setWarningMessage(props.maxCharsWarningMessage ?? `You exceeded the characters limit. Please input less than ${props.maxChars} characters.`);
      setIsSendButtonDisabled(true);
      return;
    }

    props.onInputChange(inputValue);
    setWarningMessage('');
    setIsSendButtonDisabled(false);
  }, 300);

  // Original handleInput function that calls the debounced version
  const handleInput = async (inputValue: string) => {
    setCurrentInputValue(inputValue); // Update local value immediately
    debouncedHandleInput(inputValue);
  };

  createEffect(() => {
    if (props.isUseFAQ && !localStorage.getItem(`chatwoot:${props.chatflowid}:conversation_id`)) {
      const debouncedSearch = debounce(async (inputValue: string) => {
        // Skip this search if the flag is set
        if (skipNextSearch()) {
          setSkipNextSearch(false); // Reset flag for next time
          return;
        }

        if (inputValue.trim() !== '' && props?.chatflowid && props?.apiHost) {
          const response = await searchFaqsQuery({
            apiHost: props.apiHost,
            chatflowid: props.chatflowid,
            query: inputValue,
          });
          const filtered = response.data.hits;
          setFilteredSuggestions(filtered);
          setShowSuggestions(filtered.length > 0);
        } else {
          setShowSuggestions(false);
        }
      }, 300);

      debouncedSearch(props.inputValue);

      return () => {
        debouncedSearch.cancel();
      };
    }
  });

  const checkIfInputIsValid = () => warningMessage() === '' && inputRef?.reportValidity();

  const submit = async (faqId: any = null) => {
    if (checkIfInputIsValid()) {
      // Cancel any pending debounced calls
      debouncedHandleInput.cancel();

      // Make sure we're using the most recent input value
      const latestInput = currentInputValue();
      if (props.isUseFAQ && !faqId && !localStorage.getItem(`chatwoot:${props.chatflowid}:conversation_id`)) {
        setSkipNextSearch(true);
        setShowSuggestions(false);
        setFilteredSuggestions([]);
        setIsSendButtonDisabled(true);
        setWarningMessage('');
        props.setDisableInput(true);

        const findFaq = await vectorSearchFaqsQuery({ chatflowid: props.chatflowid, apiHost: props.apiHost, query: latestInput });

        if (findFaq?.data?.hits?.length > 0) {
          faqId = findFaq?.data?.hits[0].id;
        }
      }

      // Update the parent component with the latest input value
      props.onInputChange(latestInput);

      if (props.enableInputHistory) {
        inputHistory().addToHistory(latestInput);
      }

      props.onSubmit(latestInput, undefined, faqId);

      if (props.sendMessageSound && audioRef) {
        audioRef.play();
      }
      setShowSuggestions(false);
      setFilteredSuggestions([]);
    }
  };

  const handleSuggestionClick = (question: string, id: string) => {
    setSkipNextSearch(true); // Set flag to skip the next search
    setCurrentInputValue(question);
    props.onInputChange(question);
    setWarningMessage('');
    setIsSendButtonDisabled(false);
    setShowSuggestions(false);
    setFilteredSuggestions([]);

    submit(id);
    if (inputRef) inputRef.focus();
  };

  const handleImageUploadClick = () => {
    if (imgUploadRef) imgUploadRef.click();
  };

  const handleFileUploadClick = () => {
    if (fileUploadRef) fileUploadRef.click();
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      if (props.isLoading || isSendButtonDisabled()) {
        return;
      }
      const isIMEComposition = e.isComposing || e.keyCode === 229;
      if (!isIMEComposition) {
        e.preventDefault();
        submit();
      }
    } else if (props.enableInputHistory) {
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        const previousInput = inputHistory().getPreviousInput(props.inputValue);
        props.onInputChange(previousInput);
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        const nextInput = inputHistory().getNextInput();
        props.onInputChange(nextInput);
      }
    }
  };

  // Close suggestions when clicking outside
  const handleClickOutside = (e: MouseEvent) => {
    if (suggestionsRef && !suggestionsRef.contains(e.target as Node) && inputRef && !inputRef.contains(e.target as Node)) {
      setShowSuggestions(false);
    }
  };

  createEffect(() => {
    // Keep local value synchronized with props
    setCurrentInputValue(props.inputValue);
  });

  createEffect(() => {
    const shouldAutoFocus = props.autoFocus !== undefined ? props.autoFocus : !isMobile() && window.innerWidth > 640;

    if (!props.disabled && shouldAutoFocus && inputRef) inputRef.focus();
  });

  onMount(() => {
    const shouldAutoFocus = props.autoFocus !== undefined ? props.autoFocus : !isMobile() && window.innerWidth > 640;

    if (!props.disabled && shouldAutoFocus && inputRef) inputRef.focus();

    if (props.sendMessageSound) {
      if (props.sendSoundLocation) {
        audioRef = new Audio(props.sendSoundLocation);
      } else {
        audioRef = new Audio(defaultSendSound);
      }
    }

    // Add click outside listener
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      debouncedHandleInput.cancel();
      document.removeEventListener('mousedown', handleClickOutside);
    };
  });

  const handleFileChange = (event: FileEvent<HTMLInputElement>) => {
    const file = event.target?.files?.[0];
    if (file) {
      const allowedTypes = [
        'text/csv',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/pdf',
        'text/plain',
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'image/webp',
      ];
      if (!allowedTypes.includes(file.type)) {
        setWarningMessage('Loại tệp không được hỗ trợ. Vui lòng tải lên tệp CSV, XLSX, PDF, TXT, DOCX, XLS, hoặc hình ảnh (JPEG, PNG, GIF, WEBP).');
        return;
      }
      if (file.size > 10 * 1024 * 1024) {
        // 10MB
        setWarningMessage('Tệp quá lớn. Vui lòng tải lên tệp nhỏ hơn 10MB.');
        return;
      }
      setWarningMessage('');
      props.handleFileChange(event);
    }
    if (event.target) event.target.value = '';
  };

  const getFileType = () => {
    if (props.isFullFileUpload) return '*';
    if (props.uploadsConfig?.fileUploadSizeAndTypes?.length) {
      const allowedFileTypes = props.uploadsConfig?.fileUploadSizeAndTypes.map((allowed) => allowed.fileTypes).join(',');
      if (allowedFileTypes.includes('*')) return '*';
      else return allowedFileTypes;
    }
    return '*';
  };

  return (
    <div
      class="w-full h-auto max-h-[192px] min-h-[56px] flex flex-col items-end justify-between chatbot-input border border-[#eeeeee]"
      data-testid="input"
      style={{
        margin: 'auto',
        'background-color': props.backgroundColor ?? defaultBackgroundColor,
        color: props.textColor ?? defaultTextColor,
        position: 'relative',
      }}
      onKeyDown={handleKeyDown}
    >
      <Show when={warningMessage() !== ''}>
        <div class="w-full px-4 pt-4 pb-1 text-red-500 text-sm" data-testid="warning-message">
          {warningMessage()}
        </div>
      </Show>
      <div class="w-full flex items-end justify-between">
        {props.uploadsConfig?.isImageUploadAllowed ? (
          <>
            <ImageUploadButton
              buttonColor={props.sendButtonColor}
              type="button"
              class="m-0 h-14 flex items-center justify-center"
              isDisabled={props.disabled || isSendButtonDisabled()}
              on:click={handleImageUploadClick}
            >
              <span style={{ 'font-family': 'Poppins, sans-serif' }}>Image Upload</span>
            </ImageUploadButton>
            <input
              style={{ display: 'none' }}
              multiple
              ref={imgUploadRef as HTMLInputElement}
              type="file"
              onChange={handleFileChange}
              accept={
                props.uploadsConfig?.imgUploadSizeAndTypes?.length
                  ? props.uploadsConfig?.imgUploadSizeAndTypes.map((allowed) => allowed.fileTypes).join(',')
                  : '*'
              }
            />
          </>
        ) : null}
        {props.uploadsConfig?.isRAGFileUploadAllowed || props.isFullFileUpload ? (
          <>
            <AttachmentUploadButton
              buttonColor={props.sendButtonColor}
              type="button"
              class="m-0 h-14 flex items-center justify-center"
              isDisabled={props.disabled || isSendButtonDisabled()}
              on:click={handleFileUploadClick}
            >
              <span style={{ 'font-family': 'Poppins, sans-serif' }}>File Upload</span>
            </AttachmentUploadButton>
            <input
              style={{ display: 'none' }}
              multiple
              ref={fileUploadRef as HTMLInputElement}
              type="file"
              onChange={handleFileChange}
              accept={getFileType()}
            />
          </>
        ) : null}
        <div class="relative contents">
          <ShortTextInput
            ref={inputRef as HTMLTextAreaElement}
            onInput={handleInput}
            value={props.inputValue}
            fontSize={props.fontSize}
            disabled={props.disabled}
            placeholder={props.placeholder ?? 'Nhập câu hỏi của bạn'}
          />
          <Show when={showSuggestions() && filteredSuggestions().length > 0 && props.isUseFAQ}>
            <div ref={suggestionsRef} class="absolute left-0 right-0 bottom-full mb-1 bg-white border border-gray-200 rounded shadow-lg z-10">
              <For each={filteredSuggestions()}>
                {(suggestion) => {
                  const highlightedText = suggestion?.question.replace(
                    new RegExp(`(${props.inputValue})`, 'gi'),
                    (match: string) => `<span class="bg-yellow-200">${match}</span>`,
                  );
                  return (
                    <div
                      class="px-4 py-2 hover:bg-gray-100 cursor-pointer truncate"
                      onClick={() => handleSuggestionClick(suggestion?.question, suggestion?.id)}
                      innerHTML={highlightedText}
                    />
                  );
                }}
              </For>
            </div>
          </Show>
        </div>
        {props.uploadsConfig?.isSpeechToTextEnabled ? (
          <RecordAudioButton
            buttonColor={props.sendButtonColor}
            type="button"
            class="m-0 start-recording-button h-14 flex items-center justify-center"
            isDisabled={props.disabled || isSendButtonDisabled()}
            on:click={props.onMicrophoneClicked}
          >
            <span style={{ 'font-family': 'Poppins, sans-serif' }}>Record Audio</span>
          </RecordAudioButton>
        ) : null}
        <SendButton
          sendButtonColor={props.sendButtonColor}
          type="button"
          isDisabled={props.isLoading || isSendButtonDisabled()}
          class="m-0 h-14 flex items-center justify-center"
          on:click={() => submit()}
        >
          <span style={{ 'font-family': 'Poppins, sans-serif' }}>Send</span>
        </SendButton>
      </div>
    </div>
  );
};
