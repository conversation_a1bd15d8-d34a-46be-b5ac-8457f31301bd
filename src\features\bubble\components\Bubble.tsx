import { createEffect, createSignal, onCleanup, onMount, Show, splitProps } from 'solid-js';
import styles from '../../../assets/index.css';
import { BubbleButton } from './BubbleButton';
import { BubbleParams } from '../types';
import { Bot, BotProps } from '../../../components/Bot';
import Tooltip from './Tooltip';
import { getBubbleButtonSize } from '@/utils';

const defaultButtonColor = '#3B81F6';
const defaultIconColor = 'white';

export type BubbleProps = BotProps & BubbleParams;

export const Bubble = (props: BubbleProps) => {
  const [bubbleProps] = splitProps(props, ['theme']);

  const [isBotOpened, setIsBotOpened] = createSignal(false);
  const [isBotStarted, setIsBotStarted] = createSignal(false);
  const [isFullScreen, setIsFullScreen] = createSignal(false);
  const [buttonPosition, setButtonPosition] = createSignal({
    bottom: bubbleProps.theme?.button?.bottom ?? 20,
    right: bubbleProps.theme?.button?.right ?? 20,
  });
  // Get initial width from localStorage or theme/default
  const getInitialWidth = () => {
    try {
      const savedWidth = localStorage.getItem('cmcts:chatWindowWidth');
      if (savedWidth) {
        const parsedWidth = parseInt(savedWidth, 10);
        if (!isNaN(parsedWidth) && parsedWidth >= 400 && parsedWidth <= 800) {
          return parsedWidth;
        }
      }
    } catch (error) {
      console.warn('Failed to read chat window width from localStorage:', error);
    }
    return bubbleProps.theme?.chatWindow?.width ?? 400;
  };

  const [chatWindowWidth, setChatWindowWidth] = createSignal(getInitialWidth());
  const [isResizing, setIsResizing] = createSignal(false);

  // Scale management
  const getInitialScale = () => {
    try {
      const savedScale = localStorage.getItem('cmcts:chatScale');
      if (savedScale) {
        const parsedScale = parseInt(savedScale, 10);
        if (!isNaN(parsedScale) && parsedScale >= 50 && parsedScale <= 200) {
          return parsedScale;
        }
      }
    } catch (error) {
      console.warn('Failed to read chat scale from localStorage:', error);
    }
    return 100; // Default 100%
  };

  const [scale, setScale] = createSignal(getInitialScale());
  const SCALE_STEP = 5;
  const MIN_SCALE = 80;
  const MAX_SCALE = 120;

  // Save width to localStorage
  const saveWidthToStorage = (width: number) => {
    try {
      localStorage.setItem('cmcts:chatWindowWidth', width.toString());
    } catch (error) {
      console.warn('Failed to save chat window width to localStorage:', error);
    }
  };

  // Save scale to localStorage
  const saveScaleToStorage = (scaleValue: number) => {
    try {
      localStorage.setItem('cmcts:chatScale', scaleValue.toString());
    } catch (error) {
      console.warn('Failed to save chat scale to localStorage:', error);
    }
  };

  // Scale control functions
  const increaseScale = () => {
    const newScale = Math.min(scale() + SCALE_STEP, MAX_SCALE);
    setScale(newScale);
    saveScaleToStorage(newScale);
  };

  const decreaseScale = () => {
    const newScale = Math.max(scale() - SCALE_STEP, MIN_SCALE);
    setScale(newScale);
    saveScaleToStorage(newScale);
  };

  const openBot = () => {
    if (!isBotStarted()) setIsBotStarted(true);
    setIsBotOpened(true);

    // Trigger user info reload when popup opens in popup mode
    try {
      if (typeof window !== 'undefined' && (window as any).refreshChatbotAuth) {
        console.log('openBot: Triggering authentication refresh for popup mode');
        (window as any).refreshChatbotAuth();
      }
    } catch (error) {
      console.error('openBot: Error triggering authentication refresh', error);
    }
  };

  const closeBot = () => {
    setIsBotOpened(false);
    setIsFullScreen(false);
    document.body.style.overflow = 'auto';
  };

  const toggleBot = () => {
    isBotOpened() ? closeBot() : openBot();
  };

  const toggleFullScreen = () => {
    const newFullScreenState = !isFullScreen();
    setIsFullScreen(newFullScreenState);

    if (newFullScreenState) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  };

  // Handle ESC key to exit full-screen
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && isFullScreen()) {
      setIsFullScreen(false);
      document.body.style.overflow = 'auto';
    }
  };

  onMount(() => {
    document.addEventListener('keydown', handleKeyDown);
  });

  onCleanup(() => {
    document.removeEventListener('keydown', handleKeyDown);
  });

  // Resize functionality
  let dragStartX: number;
  let initialWidth: number;

  const onResizeStart = (e: MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);
    dragStartX = e.clientX;
    initialWidth = chatWindowWidth();

    document.addEventListener('mousemove', onResizeMove);
    document.addEventListener('mouseup', onResizeEnd);
    document.body.style.cursor = 'ew-resize';
    document.body.style.userSelect = 'none';
  };

  const onResizeMove = (e: MouseEvent) => {
    if (!isResizing()) return;

    const deltaX = dragStartX - e.clientX;
    const newWidth = initialWidth + deltaX;

    // Set minimum and maximum width constraints
    const minWidth = 400;
    const maxWidth = Math.min(800, window.innerWidth - 100);

    const constrainedWidth = Math.min(Math.max(newWidth, minWidth), maxWidth);
    setChatWindowWidth(constrainedWidth);
  };

  const onResizeEnd = () => {
    setIsResizing(false);
    document.removeEventListener('mousemove', onResizeMove);
    document.removeEventListener('mouseup', onResizeEnd);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';

    // Save the final width to localStorage
    saveWidthToStorage(chatWindowWidth());
  };

  onCleanup(() => {
    setIsBotStarted(false);
    // Clean up resize event listeners
    document.removeEventListener('mousemove', onResizeMove);
    document.removeEventListener('mouseup', onResizeEnd);
    document.removeEventListener('keydown', handleKeyDown);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    document.body.style.overflow = 'auto';
  });

  const buttonSize = getBubbleButtonSize(props.theme?.button?.size); // Default to 48px if size is not provided
  const buttonBottom = props.theme?.button?.bottom ?? 20;
  const chatWindowBottom = buttonBottom + buttonSize + 10; // Adjust the offset here for slight shift

  // Add viewport meta tag dynamically
  createEffect(() => {
    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, initial-scale=1.0, interactive-widget=resizes-content';
    document.head.appendChild(meta);

    return () => {
      document.head.removeChild(meta);
    };
  });

  const showTooltip = bubbleProps.theme?.tooltip?.showTooltip ?? false;

  return (
    <>
      <Show when={props.theme?.customCSS}>
        <style>{props.theme?.customCSS}</style>
      </Show>
      <style>{styles}</style>
      <Tooltip
        showTooltip={showTooltip && !isBotOpened()}
        position={buttonPosition()}
        buttonSize={buttonSize}
        tooltipMessage={bubbleProps.theme?.tooltip?.tooltipMessage}
        tooltipBackgroundColor={bubbleProps.theme?.tooltip?.tooltipBackgroundColor}
        tooltipTextColor={bubbleProps.theme?.tooltip?.tooltipTextColor}
        tooltipFontSize={bubbleProps.theme?.tooltip?.tooltipFontSize} // Set the tooltip font size
      />
      <Show when={!isFullScreen()}>
        <BubbleButton
          {...bubbleProps.theme?.button}
          toggleBot={toggleBot}
          isBotOpened={isBotOpened()}
          setButtonPosition={setButtonPosition}
          dragAndDrop={bubbleProps.theme?.button?.dragAndDrop ?? false}
          autoOpen={bubbleProps.theme?.button?.autoWindowOpen?.autoOpen ?? false}
          openDelay={bubbleProps.theme?.button?.autoWindowOpen?.openDelay}
          autoOpenOnMobile={bubbleProps.theme?.button?.autoWindowOpen?.autoOpenOnMobile ?? false}
        />
      </Show>
      <div
        part="bot"
        style={{
          height: isFullScreen()
            ? '100vh'
            : bubbleProps.theme?.chatWindow?.height
              ? `${bubbleProps.theme?.chatWindow?.height.toString()}px`
              : 'calc(100% - 150px)',
          width: isFullScreen() ? '100vw' : `${chatWindowWidth()}px`,
          transition: isResizing() ? 'none' : 'transform 200ms cubic-bezier(0, 1.2, 1, 1), opacity 150ms ease-out',
          'transform-origin': isFullScreen() ? 'center' : 'bottom right',
          transform: isBotOpened() ? 'scale3d(1, 1, 1)' : 'scale3d(0, 0, 1)',
          'box-shadow': isFullScreen() ? 'none' : 'rgb(0 0 0 / 16%) 0px 5px 40px',
          'background-color': bubbleProps.theme?.chatWindow?.backgroundColor || '#ffffff',
          'background-image': bubbleProps.theme?.chatWindow?.backgroundImage ? `url(${bubbleProps.theme?.chatWindow?.backgroundImage})` : 'none',
          'background-size': 'cover',
          'background-position': 'center',
          'background-repeat': 'no-repeat',
          'z-index': 42424242,
          bottom: isFullScreen() ? '0' : `${Math.min(buttonPosition().bottom + buttonSize + 10, window.innerHeight - chatWindowBottom)}px`,
          right: isFullScreen() ? '0' : `${Math.min(buttonPosition().right, window.innerWidth - Math.max(chatWindowWidth() + 10, 410))}px`,
          top: isFullScreen() ? '0' : 'auto',
          left: isFullScreen() ? '0' : 'auto',
        }}
        class={
          `fixed ${isFullScreen() ? 'inset-0' : 'sm:right-5 rounded-lg'} w-full ${isFullScreen() ? 'max-h-none' : 'max-h-[704px]'}` +
          (isBotOpened() ? ' opacity-1' : ' opacity-0 pointer-events-none') +
          (isFullScreen() ? '' : ` bottom-${chatWindowBottom}px`)
        }
      >
        <Show when={isBotStarted()}>
          <div class="relative h-full">
            {/* Resize handle - OUTSIDE of scaled content */}
            <Show when={isBotOpened()}>
              <div
                onMouseDown={onResizeStart}
                class="absolute left-0 top-0 w-1 h-full cursor-ew-resize z-50 hover:bg-blue-500 hover:bg-opacity-30 transition-colors duration-200"
                style={{
                  'background-color': isResizing() ? 'rgba(59, 130, 246, 0.3)' : 'transparent',
                }}
                title="Drag to resize width"
              />
            </Show>
            {/* ALL CONTENT - SCALED (including buttons) */}
            <div
              style={{
                transform: `scale(${scale() / 100})`,
                'transform-origin': 'top left',
                width: `${100 / (scale() / 100)}%`,
                height: `${100 / (scale() / 100)}%`,
                transition: 'transform 0.2s ease-out',
              }}
            >
              <Show when={isBotOpened()}>
                {/* Scale Controls */}
                <div class="absolute top-1 left-2 flex items-center gap-1 m-[6px] z-50">
                  <button
                    onClick={decreaseScale}
                    disabled={scale() <= MIN_SCALE}
                    class="w-8 h-8 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-full flex items-center justify-center transition-all duration-200 hover:opacity-80"
                    title={`Decrease scale (${scale()}%)`}
                  >
                    <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                      <path d="M19 13H5v-2h14v2z" />
                    </svg>
                  </button>
                  <span class="text-white text-xs font-medium px-2 py-1">{scale()}%</span>
                  <button
                    onClick={increaseScale}
                    disabled={scale() >= MAX_SCALE}
                    class="w-8 h-8 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-full flex items-center justify-center transition-all duration-200 hover:opacity-80"
                    title={`Increase scale (${scale()}%)`}
                  >
                    <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                      <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                    </svg>
                  </button>
                </div>

                {/* Close button */}
                <button
                  onClick={closeBot}
                  class="py-2 pr-3 absolute top-0 right-[-8px] m-[6px] bg-transparent text-white rounded-full z-50 disabled:opacity-50 disabled:cursor-not-allowed disabled:brightness-100 transition-all filter hover:brightness-90 active:brightness-75"
                  title="Close Chat"
                >
                  <svg viewBox="0 0 24 24" width="24" height="24">
                    <path
                      fill={bubbleProps.theme?.button?.iconColor ?? defaultIconColor}
                      d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"
                    />
                  </svg>
                </button>
              </Show>

              <Bot
                badgeBackgroundColor={bubbleProps.theme?.chatWindow?.backgroundColor}
                bubbleBackgroundColor={bubbleProps.theme?.button?.backgroundColor ?? defaultButtonColor}
                bubbleTextColor={bubbleProps.theme?.button?.iconColor ?? defaultIconColor}
                showTitle={bubbleProps.theme?.chatWindow?.showTitle}
                showAgentMessages={bubbleProps.theme?.chatWindow?.showAgentMessages}
                title={bubbleProps.theme?.chatWindow?.title}
                titleAvatarSrc={bubbleProps.theme?.chatWindow?.titleAvatarSrc}
                welcomeMessage={bubbleProps.theme?.chatWindow?.welcomeMessage}
                secondMessage={bubbleProps.theme?.chatWindow?.secondMessage}
                errorMessage={bubbleProps.theme?.chatWindow?.errorMessage}
                poweredByTextColor={bubbleProps.theme?.chatWindow?.poweredByTextColor}
                textInput={bubbleProps.theme?.chatWindow?.textInput}
                botMessage={bubbleProps.theme?.chatWindow?.botMessage}
                userMessage={bubbleProps.theme?.chatWindow?.userMessage}
                feedback={bubbleProps.theme?.chatWindow?.feedback}
                fontSize={bubbleProps.theme?.chatWindow?.fontSize}
                footer={bubbleProps.theme?.chatWindow?.footer}
                sourceDocsTitle={bubbleProps.theme?.chatWindow?.sourceDocsTitle}
                starterPrompts={bubbleProps.theme?.chatWindow?.starterPrompts}
                starterPromptFontSize={bubbleProps.theme?.chatWindow?.starterPromptFontSize}
                chatflowid={props.chatflowid}
                chatflowConfig={props.chatflowConfig}
                apiHost={props.apiHost}
                onRequest={props.onRequest}
                observersConfig={props.observersConfig}
                clearChatOnReload={bubbleProps.theme?.chatWindow?.clearChatOnReload}
                disclaimer={bubbleProps.theme?.disclaimer}
                dateTimeToggle={bubbleProps.theme?.chatWindow?.dateTimeToggle}
                renderHTML={props.theme?.chatWindow?.renderHTML}
                chatwootUrl={props.chatwootUrl}
                roomIds={props.roomIds}
                isUseFAQ={props.isUseFAQ}
                filePath={props.filePath}
                suggestQuestions={props.suggestQuestions}
                externalData={props.externalData}
                callBackFunction={props.callBackFunction}
                callBackIcon={props.callBackIcon}
                onToggleFullScreen={toggleFullScreen}
                isFullScreen={isFullScreen()}
              />
            </div>
          </div>
        </Show>
      </div>
    </>
  );
};
