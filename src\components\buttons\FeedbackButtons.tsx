import { JSX, Show } from 'solid-js';
import { Spinner } from './SendButton';
import { ClipboardIcon, ThumbsDownIcon, ThumbsUpIcon } from '../icons';

type RatingButtonProps = {
  feedbackColor?: string;
  activeBackgroundColor?: string;
  isDisabled?: boolean;
  isLoading?: boolean;
  disableIcon?: boolean;
  rating?: string;
} & JSX.ButtonHTMLAttributes<HTMLButtonElement>;

const defaultFeedbackColor = '#3B81F6';

export const CopyToClipboardButton = (props: RatingButtonProps) => {
  return (
    <button
      disabled={props.isDisabled || props.isLoading}
      {...props}
      class={
        'p-2 justify-center font-semibold text-white focus:outline-none flex items-center disabled:opacity-50 disabled:cursor-not-allowed disabled:brightness-100 transition-all filter hover:brightness-90 active:brightness-75 chatbot-button ' +
        props.class
      }
      style={{ background: 'transparent', border: 'none' }}
      title="Copy to clipboard"
    >
      <Show when={!props.isLoading} fallback={<Spinner class="text-white" />}>
        <ClipboardIcon color={props.feedbackColor ?? defaultFeedbackColor} class={'send-icon flex ' + (props.disableIcon ? 'hidden' : '')} />
      </Show>
    </button>
  );
};

export const ThumbsUpButton = (props: RatingButtonProps) => {
  const isActive = () => props.rating === 'THUMBS_UP';

  return (
    <button
      type="submit"
      disabled={props.isDisabled || props.isLoading}
      {...props}
      class={
        `p-2 justify-center font-semibold focus:outline-none flex items-center disabled:opacity-50 disabled:cursor-not-allowed disabled:brightness-100 transition-all filter hover:brightness-90 active:brightness-75 rounded-full ` +
        props.class
      }
      style={{
        background: isActive() ? (props.activeBackgroundColor ?? '#00A5F020') : 'transparent',
        border: 'none',
      }}
    >
      <Show when={!props.isLoading} fallback={<Spinner class="text-white" />}>
        <ThumbsUpIcon
          color={props.feedbackColor ?? defaultFeedbackColor}
          class={'send-icon flex ' + (props.disableIcon ? 'hidden' : '')}
          filled={isActive()}
        />
      </Show>
    </button>
  );
};

export const ThumbsDownButton = (props: RatingButtonProps) => {
  const isActive = () => props.rating === 'THUMBS_DOWN';

  return (
    <button
      type="submit"
      disabled={props.isDisabled || props.isLoading}
      {...props}
      class={
        `p-2 justify-center font-semibold focus:outline-none flex items-center disabled:opacity-50 disabled:cursor-not-allowed disabled:brightness-100 transition-all filter hover:brightness-90 active:brightness-75 rounded-full ` +
        props.class
      }
      style={{
        background: isActive() ? (props.activeBackgroundColor ?? '#EC808D20') : 'transparent',
        border: 'none',
      }}
    >
      <Show when={!props.isLoading} fallback={<Spinner class="text-white" />}>
        <ThumbsDownIcon
          color={props.feedbackColor ?? defaultFeedbackColor}
          class={'send-icon flex ' + (props.disableIcon ? 'hidden' : '')}
          filled={isActive()}
        />
      </Show>
    </button>
  );
};
