export const isNotDefined = <T>(value: T | undefined | null): value is undefined | null => value === undefined || value === null;

export const isDefined = <T>(value: T | undefined | null): value is NonNullable<T> => value !== undefined && value !== null;

export const isEmpty = (value: string | undefined | null): value is undefined => value === undefined || value === null || value === '';

export const isNotEmpty = (value: string | undefined | null): value is string => value !== undefined && value !== null && value !== '';

/**
 * Clears authentication data from localStorage
 * Used when 401 errors are detected or user logs out
 */
export const clearAuthenticationData = (): void => {
  try {
    localStorage.removeItem('dataLogin');
    localStorage.removeItem('cmcts:userInfo');
    console.log('clearAuthenticationData: Authentication data cleared from localStorage');
  } catch (error) {
    console.error('clearAuthenticationData: Failed to clear authentication data', error);
  }
};

export const sendRequest = async <ResponseData>(
  params:
    | {
        url: string;
        method: string;
        body?: Record<string, unknown> | FormData;
        type?: string;
        headers?: Record<string, any>;
        formData?: FormData;
        onRequest?: (request: RequestInit) => Promise<void>;
      }
    | string,
): Promise<{ data?: ResponseData; error?: Error }> => {
  try {
    const url = typeof params === 'string' ? params : params.url;
    const headers =
      typeof params !== 'string' && isDefined(params.body)
        ? {
            'Content-Type': 'application/json',
            ...params.headers,
          }
        : undefined;
    let body: string | FormData | undefined = typeof params !== 'string' && isDefined(params.body) ? JSON.stringify(params.body) : undefined;
    if (typeof params !== 'string' && params.formData) body = params.formData;

    const requestInfo: RequestInit = {
      method: typeof params === 'string' ? 'GET' : params.method,
      mode: 'cors',
      headers,
      body,
    };

    if (typeof params !== 'string' && params.onRequest) {
      await params.onRequest(requestInfo);
    }

    const response = await fetch(url, requestInfo);

    let data: any;
    const contentType = response.headers.get('Content-Type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else if (typeof params !== 'string' && params.type === 'blob') {
      data = await response.blob();
    } else {
      data = await response.text();
    }
    if (!response.ok) {
      // Handle 401 Unauthorized responses by clearing authentication data
      if (response.status === 401) {
        console.warn('sendRequest: 401 Unauthorized detected, clearing authentication data');
        clearAuthenticationData();
      }

      let errorMessage;

      if (typeof data === 'object' && 'error' in data) {
        errorMessage = data.error;
      } else {
        errorMessage = data || response.statusText;
      }

      throw errorMessage;
    }

    return { data };
  } catch (e) {
    console.error(e);
    return { error: e as Error };
  }
};

export const setLocalStorageChatflow = (chatflowid: string, chatId: string, saveObj: Record<string, any> = {}) => {
  const chatDetails = localStorage.getItem(`${chatflowid}_EXTERNAL`);
  const obj = { ...saveObj };
  if (chatId) obj.chatId = chatId;

  if (!chatDetails) {
    localStorage.setItem(`${chatflowid}_EXTERNAL`, JSON.stringify(obj));
  } else {
    try {
      const parsedChatDetails = JSON.parse(chatDetails);
      localStorage.setItem(`${chatflowid}_EXTERNAL`, JSON.stringify({ ...parsedChatDetails, ...obj }));
    } catch (e) {
      const chatId = chatDetails;
      obj.chatId = chatId;
      localStorage.setItem(`${chatflowid}_EXTERNAL`, JSON.stringify(obj));
    }
  }
};

export const getLocalStorageChatflow = (chatflowid: string) => {
  const chatDetails = localStorage.getItem(`${chatflowid}_EXTERNAL`);
  if (!chatDetails) return {};
  try {
    return JSON.parse(chatDetails);
  } catch (e) {
    return {};
  }
};

export const removeLocalStorageChatHistory = (chatflowid: string) => {
  const keysToRemove = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('chatwoot:')) {
      keysToRemove.push(key);
    }
  }
  keysToRemove.forEach((key) => localStorage.removeItem(key));

  const chatDetails = localStorage.getItem(`${chatflowid}_EXTERNAL`);
  if (!chatDetails) return;
  try {
    const parsedChatDetails = JSON.parse(chatDetails);
    if (parsedChatDetails.lead) {
      // Dont remove lead when chat is cleared
      const obj = { lead: parsedChatDetails.lead };
      localStorage.removeItem(`${chatflowid}_EXTERNAL`);
      localStorage.setItem(`${chatflowid}_EXTERNAL`, JSON.stringify(obj));
    } else {
      localStorage.removeItem(`${chatflowid}_EXTERNAL`);
    }
  } catch (e) {
    return;
  }
};

export const getBubbleButtonSize = (size: 'small' | 'medium' | 'large' | number | undefined) => {
  if (!size) return 64;
  if (typeof size === 'number') return size;
  if (size === 'small') return 32;
  if (size === 'medium') return 48;
  if (size === 'large') return 64;
  return 64;
};

export const setCookie = (cname: string, cvalue: string, exdays: number) => {
  const d = new Date();
  d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
  const expires = 'expires=' + d.toUTCString();
  document.cookie = cname + '=' + cvalue + ';' + expires + ';path=/';
};

/**
 * Validates dataLogin from localStorage and maps user data to cmcts:userInfo
 * @returns true if successful mapping occurred, false otherwise
 */
/**
 * Type definitions for user info API response
 */
export type UserInfoApiResponse = {
  RESULT: {
    CODE: number;
    MSG: string;
  };
  OBJRETURN: {
    data: {
      id: string;
      _id: number;
      loginname: string;
      fullname: string;
      fullnameen: string;
      email: string;
      code: string;
      mobile?: string;
      imageurl?: string;
      roleName: string[];
      techGroup: Array<{
        id: number;
        name: string;
      }>;
      permissions: Record<string, string>;
      [key: string]: any;
    };
  };
};

/**
 * Fetches user info from /api/user-info endpoint with cookies
 * @param apiHost - The API host URL
 * @param onRequest - Optional request interceptor
 * @returns Promise with user info data or error
 */
export const fetchUserInfo = async (
  apiHost: string,
  onRequest?: (request: RequestInit) => Promise<void>,
): Promise<{ data?: UserInfoApiResponse; error?: Error }> => {
  try {
    // Use sendRequest for consistent 401 handling
    const result = await sendRequest<UserInfoApiResponse>({
      url: '/api/user-info',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      onRequest: async (requestInfo) => {
        // Set credentials to include cookies
        requestInfo.credentials = 'include';
        if (onRequest) {
          await onRequest(requestInfo);
        }
      },
    });

    return result;
  } catch (error) {
    console.error('fetchUserInfo: Failed to fetch user info', error);
    return { error: error as Error };
  }
};

/**
 * Maps API response to dataLogin format expected by the application
 * @param apiResponse - The API response from /api/user-info
 * @returns Mapped dataLogin object
 */
export const mapApiResponseToDataLogin = (apiResponse: UserInfoApiResponse): any => {
  try {
    const userData = apiResponse.OBJRETURN.data;

    // Create the dataLogin structure expected by the application
    const dataLogin = {
      user: {
        id: userData.id,
        username: userData.loginname,
        email: userData.email,
        phoneNumber: userData.mobile || undefined,
        role: userData.roleName?.[0] || undefined,
        groupname: userData.techGroup?.[0]?.name || undefined,
        displayPrefixes: userData.fullname || userData.fullnameen || undefined,
        active: true,
        // Include additional fields from API response
        _id: userData._id,
        fullname: userData.fullname,
        fullnameen: userData.fullnameen,
        code: userData.code,
        imageurl: userData.imageurl,
        roleName: userData.roleName,
        techGroup: userData.techGroup,
        permissions: userData.permissions,
      },
      // Add any tokens or additional auth data if needed
      accessToken: undefined, // Will be set if available in response
      refreshToken: undefined, // Will be set if available in response
    };

    return dataLogin;
  } catch (error) {
    console.error('mapApiResponseToDataLogin: Failed to map API response', error);
    throw new Error('Failed to map API response to dataLogin format');
  }
};

/**
 * Validates dataLogin from localStorage and maps user data to cmcts:userInfo
 * @returns true if successful mapping occurred, false otherwise
 */
export const validateAndMapUserInfo = (): boolean => {
  try {
    // Check if dataLogin exists
    const dataLogin = localStorage?.getItem('dataLogin');
    if (!dataLogin) {
      console.warn('validateAndMapUserInfo: dataLogin not found in localStorage');
      return false;
    }

    // Parse dataLogin JSON
    let parsedData: any;
    try {
      parsedData = JSON.parse(dataLogin);
    } catch (parseError) {
      console.error('validateAndMapUserInfo: Failed to parse dataLogin JSON', parseError);
      return false;
    }

    // Validate user object exists
    if (!parsedData || typeof parsedData !== 'object' || !parsedData.user) {
      console.warn('validateAndMapUserInfo: Invalid dataLogin structure - missing user object');
      return false;
    }

    const user = parsedData.user;
    if (typeof user !== 'object') {
      console.warn('validateAndMapUserInfo: user is not an object');
      return false;
    }

    // Map user data to UserInfo structure
    const userInfo: Record<string, any> = {};

    // Map id (any type as per UserInfo definition)
    if (isDefined(user.id)) {
      userInfo.id = user.id;
    }

    // Map name (string) - try multiple field names
    if (isNotEmpty(user.fullname)) {
      userInfo.name = user.fullname;
    } else if (isNotEmpty(user.username)) {
      userInfo.name = user.username;
    } else if (isNotEmpty(user.displayPrefixes)) {
      userInfo.name = user.displayPrefixes;
    }

    // Map email (string)
    if (isNotEmpty(user.email)) {
      userInfo.email = user.email;
    }

    // Map phone number - check multiple possible field names
    if (isNotEmpty(user.phoneNumber)) {
      userInfo.phoneNumber = user.phoneNumber;
    } else if (isNotEmpty(user.phone)) {
      userInfo.phoneNumber = user.phone;
    } else if (isNotEmpty(user.phone_number)) {
      userInfo.phoneNumber = user.phone_number;
    } else if (isNotEmpty(user.mobile)) {
      userInfo.phoneNumber = user.mobile;
    }

    // Add additional properties (index signature support)
    const additionalFields = ['role', 'groupname', 'displayPrefixes', 'active', '_id', 'code', 'roleName', 'techGroup', 'permissions'];
    additionalFields.forEach((field) => {
      if (isDefined(user[field])) {
        userInfo[field] = user[field];
      }
    });

    // Only proceed if we have at least one meaningful field
    if (Object.keys(userInfo).length === 0) {
      console.warn('validateAndMapUserInfo: No valid user data found to map');
      return false;
    }

    // Store in localStorage as cmcts:userInfo
    try {
      localStorage.setItem('cmcts:userInfo', JSON.stringify(userInfo));
      console.log('validateAndMapUserInfo: Successfully mapped user data to cmcts:userInfo', userInfo);
      return true;
    } catch (storageError) {
      console.error('validateAndMapUserInfo: Failed to store userInfo in localStorage', storageError);
      return false;
    }
  } catch (error) {
    console.error('validateAndMapUserInfo: Unexpected error', error);
    return false;
  }
};

/**
 * Checks and initializes authentication for popup embedding
 * If dataLogin doesn't exist, fetches user info from API and creates it
 * @param apiHost - The API host URL
 * @param onRequest - Optional request interceptor
 * @param isFullPage - Whether the chatbot is in full page mode
 * @returns Promise<boolean> - true if authentication is successful, false otherwise
 */
export const checkAndInitializeAuth = async (
  apiHost?: string,
  onRequest?: (request: RequestInit) => Promise<void>,
  isFullPage?: boolean,
): Promise<boolean> => {
  try {
    if (!window.location.host.includes('.vib') || isFullPage) return false;

    // First check if dataLogin already exists
    const existingDataLogin = localStorage?.getItem('dataLogin');
    if (existingDataLogin) {
      console.log('checkAndInitializeAuth: dataLogin already exists, validating and mapping');
      return validateAndMapUserInfo();
    }

    // If no dataLogin and no apiHost provided, cannot proceed
    if (!apiHost) {
      console.warn('checkAndInitializeAuth: No dataLogin found and no apiHost provided for fetching user info');
      return false;
    }

    console.log('checkAndInitializeAuth: No dataLogin found, fetching user info from API');

    // Fetch user info from API
    const { data: userInfoResponse, error } = await fetchUserInfo(apiHost, onRequest);

    if (error) {
      console.error('checkAndInitializeAuth: Failed to fetch user info from API', error);
      return false;
    }

    if (!userInfoResponse) {
      console.error('checkAndInitializeAuth: No data received from user info API');
      return false;
    }

    // Validate API response structure
    if (!userInfoResponse.RESULT || userInfoResponse.RESULT.CODE !== 0) {
      console.error('checkAndInitializeAuth: API returned error', userInfoResponse.RESULT);
      return false;
    }

    if (!userInfoResponse.OBJRETURN?.data) {
      console.error('checkAndInitializeAuth: Invalid API response structure - missing user data');
      return false;
    }

    // Map API response to dataLogin format
    const dataLogin = mapApiResponseToDataLogin(userInfoResponse);

    // Store dataLogin in localStorage
    try {
      localStorage.setItem('dataLogin', JSON.stringify(dataLogin));
      console.log('checkAndInitializeAuth: Successfully stored dataLogin from API response');
    } catch (storageError) {
      console.error('checkAndInitializeAuth: Failed to store dataLogin in localStorage', storageError);
      return false;
    }

    // Validate and map to cmcts:userInfo
    const mappingSuccess = validateAndMapUserInfo();
    if (mappingSuccess) {
      console.log('checkAndInitializeAuth: Authentication initialization completed successfully');
    } else {
      console.warn('checkAndInitializeAuth: Authentication data stored but mapping to cmcts:userInfo failed');
    }

    return mappingSuccess;
  } catch (error) {
    console.error('checkAndInitializeAuth: Unexpected error during authentication initialization', error);
    return false;
  }
};

/**
 * Forces reload of user information from API and remaps to dataLogin
 * Used when popup opens to ensure fresh user data
 * @param apiHost - The API host URL
 * @param onRequest - Optional request interceptor
 * @param isFullPage - Whether the chatbot is in full page mode
 * @returns Promise<boolean> - true if reload and mapping successful, false otherwise
 */
export const reloadAndMapUserInfo = async (
  apiHost?: string,
  onRequest?: (request: RequestInit) => Promise<void>,
  isFullPage?: boolean,
): Promise<boolean> => {
  try {
    if (!window.location.host.includes('.vib') || isFullPage) return false;

    if (!apiHost) {
      console.warn('reloadAndMapUserInfo: No apiHost provided for fetching user info');
      return false;
    }

    console.log('reloadAndMapUserInfo: Forcing reload of user info from API');

    // Fetch fresh user info from API
    const { data: userInfoResponse, error } = await fetchUserInfo(apiHost, onRequest);

    if (error) {
      console.error('reloadAndMapUserInfo: Failed to fetch user info from API', error);
      // If 401 error, authentication data should already be cleared by sendRequest
      return false;
    }

    if (!userInfoResponse) {
      console.error('reloadAndMapUserInfo: No data received from user info API');
      return false;
    }

    // Validate API response structure
    if (!userInfoResponse.RESULT || userInfoResponse.RESULT.CODE !== 0) {
      console.error('reloadAndMapUserInfo: API returned error', userInfoResponse.RESULT);
      return false;
    }

    if (!userInfoResponse.OBJRETURN?.data) {
      console.error('reloadAndMapUserInfo: Invalid API response structure - missing user data');
      return false;
    }

    // Map API response to dataLogin format
    const dataLogin = mapApiResponseToDataLogin(userInfoResponse);

    // Store updated dataLogin in localStorage
    try {
      localStorage.setItem('dataLogin', JSON.stringify(dataLogin));
      console.log('reloadAndMapUserInfo: Successfully updated dataLogin from API response');
    } catch (storageError) {
      console.error('reloadAndMapUserInfo: Failed to store updated dataLogin in localStorage', storageError);
      return false;
    }

    // Validate and map to cmcts:userInfo
    const mappingSuccess = validateAndMapUserInfo();
    if (mappingSuccess) {
      console.log('reloadAndMapUserInfo: User info reload and mapping completed successfully');
    } else {
      console.warn('reloadAndMapUserInfo: User data reloaded but mapping to cmcts:userInfo failed');
    }

    return mappingSuccess;
  } catch (error) {
    console.error('reloadAndMapUserInfo: Unexpected error during user info reload', error);
    return false;
  }
};

export const getCookie = (cname: string): string => {
  const name = cname + '=';
  const decodedCookie = decodeURIComponent(document.cookie);
  const ca = decodedCookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return '';
};
