import { TypingBubble } from '@/components';

type Props = {
  streamingStartTime?: number | null;
  elapsedStreamingTime?: number;
};

export const LoadingBubble = (props: Props) => (
  <div class="flex justify-start mb-2 items-start animate-fade-in host-container">
    <span class="px-4 py-4 ml-2 whitespace-pre-wrap max-w-full chatbot-host-bubble relative" data-testid="host-bubble">
      {props.streamingStartTime && props.elapsedStreamingTime && props.elapsedStreamingTime > 0 && (
        <span class="absolute -top-4 left-2 text-xs font-medium opacity-70 pointer-events-none">{props.elapsedStreamingTime.toFixed(1)}s</span>
      )}
      <TypingBubble />
    </span>
  </div>
);
