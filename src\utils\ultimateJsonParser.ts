type JsonValue = Record<string, any> | any[];

type ExtractionStrategy = () => JsonValue | undefined;

// Helper function to sort JSON keys recursively for consistent output
function sortJsonKeys(obj: JsonValue): JsonValue {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(sortJsonKeys);
  }

  const sorted: Record<string, any> = {};
  const keys = Object.keys(obj).sort();
  for (const key of keys) {
    sorted[key] = sortJsonKeys(obj[key]);
  }
  return sorted;
}

export function parseJsonMarkdown(jsonString: string): any {
  try {
    if (jsonString === null || jsonString === undefined) {
      throw new Error('Input cannot be null or undefined');
    }

    const originalInput = jsonString;
    const trimmedInput = jsonString.trim();

    if (trimmedInput === '') {
      throw new Error('Input cannot be empty');
    }

    const strategies: ExtractionStrategy[] = [
      () => extractFromMarkdownCodeBlocks(trimmedInput),
      () => extractPlainJson(trimmedInput),
      () => extractMultipleJsonObjects(trimmedInput),
      () => extractWithPriorityCheck(trimmedInput),
    ];

    let lastError: Error | null = null;

    for (let i = 0; i < strategies.length; i++) {
      const strategy = strategies[i];
      try {
        const result = strategy();
        if (result !== undefined) {
          return sortJsonKeys(result);
        }
      } catch (error) {
        lastError = error as Error;
      }
    }

    const errorDetails = [
      'Could not find valid JSON in the input.',
      `Input length: ${originalInput.length} characters`,
      `Trimmed input preview: ${trimmedInput.substring(0, 100)}${trimmedInput.length > 100 ? '...' : ''}`,
    ];

    if (lastError) {
      errorDetails.push(`Last parsing error: ${lastError.message}`);
    }

    throw new Error(errorDetails.join(' '));
  } catch {}

  return {};
}

function extractFromMarkdownCodeBlocks(text: string): JsonValue | undefined {
  const codeBlockPatterns = [/```(?:json|javascript|js|typescript|ts)?\s*\n?([\s\S]*?)```/gi, /``([^`]+)``/g, /`([^`]+)`/g];

  const foundObjects: JsonValue[] = [];
  const processedContents = new Set<string>();

  for (const pattern of codeBlockPatterns) {
    const matches = Array.from(text.matchAll(pattern));
    for (const match of matches) {
      const content = match[1]?.trim();
      if (content) {
        const cleanedContent = cleanJsonContent(content);

        // Skip if we've already processed this content
        if (processedContents.has(cleanedContent)) {
          continue;
        }
        processedContents.add(cleanedContent);

        if (isValidJsonStart(cleanedContent)) {
          try {
            const parsed = JSON.parse(cleanedContent);
            foundObjects.push(parsed);
          } catch (error) {
            // Try to fix malformed content
            if (cleanedContent.trim().startsWith('[')) {
              const fixedContent = tryFixMalformedArray(cleanedContent);
              if (fixedContent) {
                try {
                  const parsed = JSON.parse(fixedContent);
                  foundObjects.push(parsed);
                } catch (e) {
                  // Ignore if still can't parse
                }
              }
            } else if (cleanedContent.trim().startsWith('{')) {
              const fixedContent = tryFixIncompleteObjectStandalone(cleanedContent);
              if (fixedContent) {
                try {
                  const parsed = JSON.parse(fixedContent);
                  foundObjects.push(parsed);
                } catch (e) {
                  // Ignore if still can't parse
                }
              }
            }
          }
        }
      }
    }
  }

  if (foundObjects.length === 1) {
    return foundObjects[0];
  } else if (foundObjects.length > 1) {
    return foundObjects;
  }

  return undefined;
}

function extractFromBraces(text: string): JsonValue | undefined {
  return extractFromDelimiters(text, '{', '}');
}

function extractFromBrackets(text: string): JsonValue | undefined {
  return extractFromDelimiters(text, '[', ']');
}

function extractPlainJson(text: string): JsonValue | undefined {
  const cleanedText = cleanJsonContent(text);
  if (isValidJsonStart(cleanedText)) {
    try {
      return JSON.parse(cleanedText);
    } catch (error) {
      // Try to fix malformed content
      if (cleanedText.trim().startsWith('{')) {
        // First try basic key/value fixes
        const basicFixed = fixUnquotedKeysAndValues(cleanedText);
        try {
          return JSON.parse(basicFixed);
        } catch (e) {
          // If that fails, try the more complex incomplete object fix
          const fixedContent = tryFixIncompleteObjectStandalone(cleanedText);
          if (fixedContent) {
            try {
              return JSON.parse(fixedContent);
            } catch (e2) {
              return undefined;
            }
          }
        }
      } else if (cleanedText.trim().startsWith('[')) {
        const fixedContent = tryFixMalformedArray(cleanedText);
        if (fixedContent) {
          try {
            return JSON.parse(fixedContent);
          } catch (e) {
            return undefined;
          }
        }
      }
      return undefined;
    }
  }
  return undefined;
}

function extractMultipleJsonObjects(text: string): JsonValue | undefined {
  const trimmed = text.trim();

  // Look for pattern of multiple JSON objects separated by whitespace
  // Pattern: {...} {...} or {...}   {...}
  const multipleObjectPattern = /^\s*\{[\s\S]*?}\s+\{[\s\S]*?}\s*$/;

  if (!multipleObjectPattern.test(trimmed)) {
    return undefined;
  }

  // Try to extract individual JSON objects
  const objects: any[] = [];
  let currentPos = 0;

  while (currentPos < trimmed.length) {
    // Skip whitespace
    while (currentPos < trimmed.length && /\s/.test(trimmed[currentPos])) {
      currentPos++;
    }

    if (currentPos >= trimmed.length) break;

    // Find the start of a JSON object
    if (trimmed[currentPos] !== '{') {
      break;
    }

    // Find the matching closing brace
    const objectStart = currentPos;
    let braceDepth = 0;
    let inString = false;
    let escaped = false;

    while (currentPos < trimmed.length) {
      const char = trimmed[currentPos];

      if (escaped) {
        escaped = false;
      } else if (char === '\\') {
        escaped = true;
      } else if (char === '"' && !escaped) {
        inString = !inString;
      } else if (!inString) {
        if (char === '{') {
          braceDepth++;
        } else if (char === '}') {
          braceDepth--;
          if (braceDepth === 0) {
            // Found complete object
            const objectText = trimmed.slice(objectStart, currentPos + 1);
            const fixedObject = fixUnquotedKeysAndValues(objectText);
            try {
              const parsed = JSON.parse(fixedObject);
              objects.push(parsed);
              currentPos++;
              break;
            } catch (error) {
              return undefined;
            }
          }
        }
      }
      currentPos++;
    }

    if (braceDepth !== 0) {
      // Incomplete object
      return undefined;
    }
  }

  return objects.length > 1 ? objects : undefined;
}

function extractWithPriorityCheck(text: string): JsonValue | undefined {
  const trimmed = text.trim();
  let contentToCheck = trimmed.startsWith('`') ? trimmed.slice(1) : trimmed;

  if (contentToCheck.startsWith('[')) {
    return extractArrayWithFallback(trimmed, contentToCheck);
  }

  if (contentToCheck.startsWith('{')) {
    return extractFromBraces(trimmed);
  }

  const primitiveResult = extractPlainJson(trimmed);
  if (primitiveResult !== undefined) {
    return primitiveResult;
  }

  return extractFromBrackets(trimmed) || extractFromBraces(trimmed);
}

function extractArrayWithFallback(originalText: string, contentToCheck: string): JsonValue | undefined {
  const arrayResult = extractFromBrackets(originalText);

  if (arrayResult !== undefined) {
    return arrayResult;
  }

  if (originalText.startsWith('`')) {
    const arrayResultWithoutBacktick = extractFromBrackets(contentToCheck);
    if (arrayResultWithoutBacktick !== undefined) {
      return arrayResultWithoutBacktick;
    }
  }

  const fixedContent = tryFixMalformedArray(originalText.startsWith('`') ? contentToCheck : originalText);
  if (fixedContent) {
    try {
      return JSON.parse(fixedContent);
    } catch (error) {
      return undefined;
    }
  }

  return undefined;
}

function extractFromDelimiters(text: string, startChar: string, endChar: string): JsonValue | undefined {
  const startIndex = text.indexOf(startChar);
  if (startIndex === -1) return undefined;

  const content = findMatchingDelimiter(text, startIndex, startChar, endChar);
  if (content) {
    const cleanedContent = cleanJsonContent(content);
    try {
      return JSON.parse(cleanedContent);
    } catch (error) {
      return tryFixMalformedContent(cleanedContent, startChar);
    }
  }

  return tryFixIncompleteStructure(text, startChar);
}

function findMatchingDelimiter(text: string, startIndex: number, startChar: string, endChar: string): string | null {
  let depth = 0;
  let inString = false;
  let escaped = false;

  for (let i = startIndex; i < text.length; i++) {
    const char = text[i];

    if (escaped) {
      escaped = false;
      continue;
    }

    if (char === '\\') {
      escaped = true;
      continue;
    }

    if (char === '"' && !escaped) {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === startChar) {
        depth++;
      } else if (char === endChar) {
        depth--;
        if (depth === 0) {
          return text.slice(startIndex, i + 1);
        }
      }
    }
  }

  return null;
}

function tryFixMalformedContent(content: string, startChar: string): JsonValue | undefined {
  if (startChar === '[' && content.startsWith('[')) {
    const fixedContent = tryFixMalformedArray(content);
    return tryParseJson(fixedContent);
  } else if (startChar === '{' && content.startsWith('{')) {
    // First try basic key/value fixes for complete but malformed objects
    const basicFixed = fixUnquotedKeysAndValues(content);
    const basicResult = tryParseJson(basicFixed);
    if (basicResult !== undefined) {
      return basicResult;
    }

    // If that fails, try the incomplete object fix
    const fixedContent = tryFixIncompleteObjectStandalone(content);
    return tryParseJson(fixedContent);
  }
  return undefined;
}

function tryFixIncompleteStructure(text: string, startChar: string): JsonValue | undefined {
  if (startChar === '[') {
    const fixedContent = tryFixMalformedArray(text);
    return tryParseJson(fixedContent);
  } else if (startChar === '{') {
    const fixedContent = tryFixIncompleteObjectStandalone(text);
    return tryParseJson(fixedContent);
  }
  return undefined;
}

function tryParseJson(content: string | null): JsonValue | undefined {
  if (!content) return undefined;
  try {
    return JSON.parse(content);
  } catch (error) {
    return undefined;
  }
}

function cleanJsonContent(content: string): string {
  let cleaned = content
    .trim()
    .replace(/^\s*[\r\n]+/gm, '')
    .replace(/[\r\n]+\s*$/gm, '')
    .replace(/\r\n/g, '\n')
    .trim();

  // Remove leading dashes/minus signs (common in YAML-like formats)
  cleaned = cleaned.replace(/^-\s*/, '');

  // Remove comments (/* ... */, //, #) but only when not inside strings
  cleaned = removeCommentsFromJson(cleaned);

  return cleaned.trim();
}

function removeCommentsFromJson(content: string): string {
  let result = '';
  let inString = false;
  let stringChar = ''; // Track which quote character started the string
  let escaped = false;
  let i = 0;

  while (i < content.length) {
    const char = content[i];
    const nextChar = content[i + 1];

    if (escaped) {
      result += char;
      escaped = false;
      i++;
      continue;
    }

    if (char === '\\' && inString) {
      result += char;
      escaped = true;
      i++;
      continue;
    }

    // Handle both single and double quotes
    if ((char === '"' || char === "'") && !escaped) {
      if (!inString) {
        // Starting a string
        inString = true;
        stringChar = char;
      } else if (char === stringChar) {
        // Ending the string with the same quote character that started it
        inString = false;
        stringChar = '';
      }
      result += char;
      i++;
      continue;
    }

    if (!inString) {
      // Handle block comments /* ... */
      if (char === '/' && nextChar === '*') {
        // Skip until we find */ or end of content
        i += 2;
        while (i < content.length - 1) {
          if (content[i] === '*' && content[i + 1] === '/') {
            i += 2;
            break;
          }
          i++;
        }
        // If we reached the end without finding */, we're done
        if (i >= content.length - 1) {
          break;
        }
        continue;
      }

      // Handle line comments // and #
      if ((char === '/' && nextChar === '/') || char === '#') {
        // Skip to end of line or until we find a structural character
        while (i < content.length && content[i] !== '\n' && content[i] !== '\r') {
          // If we encounter JSON structural characters, we might want to preserve them
          if (content[i] === '}' || content[i] === ']' || content[i] === ',') {
            // Check if this looks like it's part of the JSON structure, not the comment
            // Simple heuristic: if we see }, or ], it's likely end of a JSON structure
            break;
          }
          i++;
        }
        continue;
      }
    }

    result += char;
    i++;
  }

  return result;
}

function isValidJsonStart(content: string): boolean {
  const trimmed = content.trim();
  return (
    trimmed.startsWith('{') ||
    trimmed.startsWith('[') ||
    trimmed.startsWith('"') ||
    trimmed === 'true' ||
    trimmed === 'false' ||
    trimmed === 'null' ||
    /^-?(\d+(\.\d+)?|\.\d+)([eE][+-]?\d+)?$/.test(trimmed)
  );
}

function tryAdvancedArrayFix(content: string): string | null {
  const trimmed = content.trim();

  if (!trimmed.startsWith('[')) {
    return null;
  }

  const innerContent = trimmed.endsWith(']') ? trimmed.slice(1, -1).trim() : trimmed.slice(1).trim();
  const objects = splitArrayObjects(innerContent);
  const fixedObjects: string[] = [];

  for (const obj of objects) {
    const fixedObj = tryFixSingleObject(obj.trim());
    if (fixedObj) {
      fixedObjects.push(fixedObj);
    }
  }

  if (fixedObjects.length > 0) {
    const result = '[' + fixedObjects.join(', ') + ']';
    return tryParseJson(result) ? result : null;
  }

  return null;
}

function splitArrayObjects(innerContent: string): string[] {
  const objects: string[] = [];
  let bestSplit = -1;

  for (let i = 0; i < innerContent.length; i++) {
    if (innerContent[i] === ',') {
      let potentialObject = innerContent.slice(0, i);
      if (!potentialObject.endsWith('}')) {
        potentialObject += '}';
      }

      try {
        JSON.parse(potentialObject);
        bestSplit = i;
        break;
      } catch (error) {}
    }
  }

  if (bestSplit > -1) {
    let firstObject = innerContent.slice(0, bestSplit);
    let remainingContent = innerContent.slice(bestSplit + 1).trim();

    if (!firstObject.endsWith('}')) {
      firstObject += '}';
    }

    objects.push(firstObject);

    if (remainingContent) {
      // Clean up malformed quotes and separators in the remaining content
      // Pattern: """" "key": value -> "key": value
      remainingContent = remainingContent.replace(/^["']*\s*/, '');

      // If the remaining content looks like a key-value pair without braces, try to extract multiple objects
      if (remainingContent.includes('"') && remainingContent.includes(':')) {
        // Try to split by pattern that looks like object boundaries
        const potentialObjects = splitRemainingObjects(remainingContent);
        objects.push(...potentialObjects);
      } else {
        objects.push(remainingContent);
      }
    }
  } else {
    objects.push(innerContent.trim());
  }

  return objects;
}

function splitRemainingObjects(content: string): string[] {
  const objects: string[] = [];

  // Look for patterns like "key": {...} that indicate complete key-value pairs with object values
  const objectPattern = /"[^"]+"\s*:\s*\{[^{}]*(?:\{[^{}]*}[^{}]*)*}/g;
  let match;
  let lastIndex = 0;

  while ((match = objectPattern.exec(content)) !== null) {
    // Add any content before this match as a separate object if it looks valid
    if (match.index > lastIndex) {
      const beforeMatch = content.slice(lastIndex, match.index).trim();
      if (beforeMatch && beforeMatch.includes(':')) {
        objects.push(beforeMatch);
      }
    }

    // Add the matched object (this is already a complete key-value pair)
    objects.push(match[0]);
    lastIndex = match.index + match[0].length;
  }

  // Add any remaining content
  if (lastIndex < content.length) {
    const remaining = content.slice(lastIndex).trim();
    if (remaining && remaining.includes(':')) {
      objects.push(remaining);
    }
  }

  // If no objects were found using the pattern, but the content looks like a key-value pair,
  // treat the entire content as a single object
  if (objects.length === 0 && content.includes(':') && content.includes('"')) {
    objects.push(content);
  }

  return objects;
}

function tryFixSingleObject(obj: string): string | null {
  if (!obj) return null;

  let fixed = obj.trim();

  if (fixed.startsWith(',')) {
    fixed = fixed.slice(1).trim();
  }

  // Special case: if the content looks like a complete key-value pair with object value
  // e.g., "lorem": {"ipsum": "sic"}, just wrap it in braces
  if (!fixed.startsWith('{') && fixed.includes(':') && fixed.includes('{') && fixed.includes('}')) {
    const wrappedFixed = '{' + fixed + '}';
    if (tryParseJson(wrappedFixed)) {
      return wrappedFixed;
    }
  }

  if (!fixed.startsWith('{')) {
    fixed = '{' + fixed;
  }

  if (!fixed.endsWith('}')) {
    fixed = fixed + '}';
  }

  fixed = fixUnquotedKeysAndValues(fixed);

  if (tryParseJson(fixed)) {
    return fixed;
  }

  return tryAggressiveObjectFix(obj.trim());
}

function tryAggressiveObjectFix(obj: string): string | null {
  if (!obj) return null;

  let fixed = obj.trim();

  if (fixed.startsWith(',')) {
    fixed = fixed.slice(1).trim();
  }

  if (!fixed.startsWith('{')) {
    fixed = '{' + fixed;
  }

  const colonIndex = fixed.indexOf(':');
  if (colonIndex > -1) {
    const keyPart = fixed.slice(0, colonIndex);
    const valuePart = fixed.slice(colonIndex + 1);

    let key = keyPart.slice(1).trim();
    if (!key.startsWith('"')) {
      key = '"' + key + '"';
    }

    let value = valuePart.trim();
    if (value.endsWith('}')) {
      value = value.slice(0, -1);
    }

    value = escapeJsonValue(value);
    value = '"' + value + '"';

    fixed = '{' + key + ':' + value + '}';

    if (tryParseJson(fixed)) {
      return fixed;
    }
  }

  fixed = fixUnquotedKeysAndValues(obj.trim());
  return tryParseJson(fixed) ? fixed : null;
}

function escapeJsonValue(value: string): string {
  return value.replace(/\\/g, '\\\\').replace(/"/g, '\\"').replace(/\n/g, '\\n').replace(/\r/g, '\\r').replace(/\t/g, '\\t');
}

function fixUnquotedKeysAndValues(obj: string): string {
  let fixed = obj;

  // Handle standalone arrays in objects by merging them into existing arrays
  fixed = mergeStandaloneArrays(fixed);

  // Fix arrays with missing commas between quoted strings
  // Pattern: ["value" "value1" "value2"] -> ["value", "value1", "value2"]
  fixed = fixed.replace(/(\[[\s\S]*?])/g, (arrayMatch) => {
    // Only process if it looks like an array with missing commas
    if (/"[^"]*"\s+"[^"]*"/.test(arrayMatch)) {
      // Simple approach: replace all instances of '" "' with '", "'
      return arrayMatch.replace(/"\s+"/g, '", "');
    }
    return arrayMatch;
  });

  // Fix nested quotes in string values
  // Pattern: "lorem "ipsum" sic" -> "lorem \"ipsum\" sic"
  fixed = fixed.replace(/:(\s*)"([^"]*)"([^"]+)"([^"]*)"(\s*[,}])/g, (match, space1, part1, middle, part2, ending) => {
    // Only apply if middle part doesn't contain colons (to avoid matching across key-value pairs)
    if (!middle.includes(':')) {
      const escapedValue = `"${part1}\\"${middle}\\"${part2}"`;
      return ':' + space1 + escapedValue + ending;
    }
    return match;
  });

  // Remove trailing commas
  fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

  // Fix malformed key patterns where comma is used instead of colon
  // Pattern: {"key", value} -> {"key": "value"} (but avoid matching valid JSON patterns)
  // Also avoid matching inside arrays by checking for square brackets
  fixed = fixed.replace(/("([^"]+)"),\s*([^:}]*?)(?=\s*}|$)/g, (match, quotedKey, _key, value, offset, string) => {
    // Only apply if the value doesn't contain a colon (to avoid matching valid JSON)
    // and if we're not inside an array (check for unmatched opening square bracket before this position)
    if (!value.includes(':') && !value.includes(']')) {
      // Check if we're inside an array by looking for unmatched square brackets before this position
      const beforeMatch = string.substring(0, offset);
      let squareBracketDepth = 0;
      let inString = false;
      let escaped = false;

      for (let i = 0; i < beforeMatch.length; i++) {
        const char = beforeMatch[i];
        if (escaped) {
          escaped = false;
          continue;
        }
        if (char === '\\') {
          escaped = true;
          continue;
        }
        if (char === '"' && !escaped) {
          inString = !inString;
          continue;
        }
        if (!inString) {
          if (char === '[') {
            squareBracketDepth++;
          } else if (char === ']') {
            squareBracketDepth--;
          }
        }
      }

      // Only apply the fix if we're not inside an array (squareBracketDepth should be 0)
      if (squareBracketDepth === 0) {
        const cleanValue = value.trim().replace(/,$/, '');
        return quotedKey + ': "' + cleanValue + '"';
      }
    }
    return match;
  });

  // Fix malformed key syntax like {"key:"value"} -> {"key": "value"}
  fixed = fixed.replace(/\{\s*"([^"]*):([^"]*)"([^"]*)"([^}]*)}/g, (_match, keyPart, _valuePart, actualValue) => {
    return `{"${keyPart}": "${actualValue}"}`;
  });

  // Fix malformed key syntax like {"key:value} -> {"key": "value"}
  fixed = fixed.replace(/\{\s*"([^"]*):([^"}]*)}/g, (_match, keyPart, valuePart) => {
    return `{"${keyPart}": "${valuePart}"}`;
  });

  // Special handling for unquoted values containing braces BEFORE fixing keys
  // This pattern specifically targets cases like {key:value{with braces}}
  fixed = fixed.replace(
    /([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*([^"{}[\],]*\{[^}]*}[^"{}[\],]*)(\s*)([,}])/g,
    (_match, prefix, key, value, space, suffix) => {
      const trimmedValue = value.trim();
      return prefix + '"' + key + '": "' + trimmedValue + '"' + space + suffix;
    },
  );

  // Fix single quoted keys
  fixed = fixed.replace(/([{,]\s*)'([^']*)'(\s*):/g, '$1"$2"$3:');

  // Fix unquoted numeric keys first (more specific)
  fixed = fixed.replace(/([{,]\s*)(\d+)(\s*):/g, '$1"$2"$3:');

  // Fix other unquoted keys (including alphanumeric keys)
  fixed = fixed.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":');

  // Fix single quoted values
  fixed = fixed.replace(/:(\s*)'([^']*)'(\s*[,}])/g, ':"$2"$3');

  // Fix unquoted values that contain commas and end with quotes (e.g., "key": value, more, content")
  // This pattern specifically looks for values ending with quotes and captures everything up to that quote
  // IMPORTANT: This must run before the simple unquoted value patterns to handle complex values correctly
  fixed = fixed.replace(/:(\s*)([^"{}[\]]*?")\s*([,}])/g, (match, space1, value, ending) => {
    const trimmedValue = value.trim();

    // Handle values that end with a quote but don't start with one
    if (trimmedValue.endsWith('"') && !trimmedValue.startsWith('"')) {
      const valueWithoutTrailingQuote = trimmedValue.slice(0, -1);
      const lowerValue = valueWithoutTrailingQuote.toLowerCase();

      // Check if it's a number
      if (/^-?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d*)?$/.test(valueWithoutTrailingQuote)) {
        return ':' + space1 + valueWithoutTrailingQuote + ending;
      }

      // Handle boolean and null values
      if (lowerValue === 'true' || lowerValue === 'false' || lowerValue === 'null') {
        return ':' + space1 + lowerValue + ending;
      }

      // Quote the value properly
      return ':' + space1 + '"' + valueWithoutTrailingQuote + '"' + ending;
    }

    // If it doesn't end with a quote, fall through to the next pattern
    return match;
  });

  // Handle boolean and null values (both lowercase and uppercase)
  fixed = fixed.replace(/:(\s*)(true|false|null|TRUE|FALSE|NULL)\s*([,}])/g, (_match, space1, value, ending) => {
    const lowerValue = value.toLowerCase();
    return ':' + space1 + lowerValue + ending;
  });

  // Fix other unquoted values (but skip boolean/null values and numbers)
  fixed = fixed.replace(/:(\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*([,}])/g, (_match, space1, value, ending) => {
    const lowerValue = value.toLowerCase();
    if (lowerValue === 'true' || lowerValue === 'false' || lowerValue === 'null') {
      return ':' + space1 + lowerValue + ending;
    }
    return ':' + space1 + '"' + value + '"' + ending;
  });

  // Fix missing commas between consecutive quoted strings (e.g., "value""key")
  fixed = fixed.replace(/"(\s*)"([^"]+)"(\s*):/g, '", "$2":');

  // Handle complex unquoted values followed by quoted keys
  // Pattern: "key": unquoted value "nextkey": nextvalue
  // But avoid matching inside quoted strings by using a more precise approach
  fixed = fixed.replace(/:\s*([^"{}[\],]*?)\s+"([^"]*)"(\s*):/g, (match, value, nextKey, space, offset, string) => {
    const trimmedValue = value.trim();
    const lowerValue = trimmedValue.toLowerCase();

    // Skip if it's a number, boolean, or null
    if (/^-?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d*)?$/.test(trimmedValue) || lowerValue === 'true' || lowerValue === 'false' || lowerValue === 'null') {
      return match;
    }

    // More robust check: look for the pattern where we have a complete key-value structure
    // that's not inside a quoted string. We need to ensure this is actually separate key-value pairs
    // and not content inside a string value.

    // Check if this pattern appears to be at the top level (not nested inside a string)
    const beforeMatch = string.substring(0, offset);

    // Count unmatched quotes to see if we're inside a string
    let quoteCount = 0;
    let inEscape = false;
    for (let i = 0; i < beforeMatch.length; i++) {
      const char = beforeMatch[i];
      if (inEscape) {
        inEscape = false;
        continue;
      }
      if (char === '\\') {
        inEscape = true;
        continue;
      }
      if (char === '"') {
        quoteCount++;
      }
    }

    // If we have an odd number of quotes, we're inside a string
    if (quoteCount % 2 === 1) {
      return match; // Don't modify content inside strings
    }

    // Additional check: make sure this looks like a real key-value pattern
    // and not just content that happens to have quotes and colons
    // Only apply if the nextKey looks like a valid identifier
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(nextKey)) {
      return match;
    }

    return ': "' + trimmedValue + '", "' + nextKey + '"' + space + ':';
  });

  // Handle malformed content after valid string values (e.g., "key": "value": extra content)
  // Extract the first valid key-value pair when there's malformed trailing content
  // But be more specific to avoid matching valid nested object patterns
  if (fixed.match(/^\{\s*"[^"]*"\s*:\s*"[^"]*"\s*:\s*\{}\s*}\s*"\s*}$/)) {
    const keyValueMatch = fixed.match(/^\{\s*"([^"]*)"\s*:\s*"([^"]*)"/);
    if (keyValueMatch) {
      const key = keyValueMatch[1];
      const value = keyValueMatch[2];
      fixed = `{"${key}": "${value}"}`;
    }
  }

  // Handle extra content after string values (e.g., "key": "value" extra)
  // This pattern looks for a complete key-value pair followed by extra content and extracts just the valid part
  fixed = fixed.replace(/^(\{\s*"[^"]*"\s*:\s*"[^"]*")\s+[^}]*}$/g, '$1}');

  // Remove unquoted text that appears between a comma and a quoted key
  // Pattern: , unquoted text "key":
  fixed = fixed.replace(/,\s*([^"{}[\],]+?)\s*"([^"]*)"(\s*):/g, ', "$2"$3:');

  // Fix unterminated strings at the end of objects
  // Pattern: "key": "value }  ->  "key": "value"}
  fixed = fixed.replace(/:\s*"([^"]*)\s*}$/g, (_match, value) => {
    return ': "' + value.trim() + '"}';
  });

  // Fix remaining unquoted values - handle complex cases with spaces and punctuation
  // But only apply this to truly unquoted values, not content inside quoted strings
  fixed = fixed.replace(/:(\s*)([^"{}[\]]*?)(\s*)(?=\s*[,}]|$)/g, (match, space1, value, space2, offset, string) => {
    const trimmedValue = value.trim();
    const lowerValue = trimmedValue.toLowerCase();

    // Skip if empty
    if (!trimmedValue) {
      return match;
    }

    // Check if we're inside a quoted string by examining the context
    const beforeMatch = string.substring(0, offset);
    let quoteCount = 0;
    let inEscape = false;
    for (let i = 0; i < beforeMatch.length; i++) {
      const char = beforeMatch[i];
      if (inEscape) {
        inEscape = false;
        continue;
      }
      if (char === '\\') {
        inEscape = true;
        continue;
      }
      if (char === '"') {
        quoteCount++;
      }
    }

    // If we have an odd number of quotes, we're inside a string - don't modify
    if (quoteCount % 2 === 1) {
      return match;
    }

    // Check if it's a number (including leading decimal, trailing decimal, and incomplete scientific notation)
    if (/^-?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d*)?$/.test(trimmedValue)) {
      // Fix leading decimal point by adding 0
      let fixedNumber = trimmedValue;
      if (fixedNumber.startsWith('.')) {
        fixedNumber = '0' + fixedNumber;
      } else if (fixedNumber.startsWith('-.')) {
        fixedNumber = '-0' + fixedNumber.slice(1);
      }
      // Fix trailing decimal point by removing it
      if (fixedNumber.endsWith('.')) {
        fixedNumber = fixedNumber.slice(0, -1);
      }
      // Fix incomplete scientific notation (e.g., "1e" -> "1")
      if (/[eE][+-]?$/.test(fixedNumber)) {
        fixedNumber = fixedNumber.replace(/[eE][+-]?$/, '');
      }
      return ':' + space1 + fixedNumber + space2;
    }

    // Skip if already quoted or is a boolean/null value
    if (trimmedValue.startsWith('"') || lowerValue === 'true' || lowerValue === 'false' || lowerValue === 'null') {
      return match;
    }

    // For complex unquoted values, extract until we hit a quote that starts a new key
    // This handles cases like: "key": value "nextkey": nextvalue
    let actualValue = trimmedValue;

    // Look for a pattern where the value contains text followed by a quote and key pattern
    const nextKeyMatch = actualValue.match(/^(.*?)\s+"[^"]*"\s*:/);
    if (nextKeyMatch) {
      actualValue = nextKeyMatch[1].trim();
    }

    return ':' + space1 + '"' + actualValue + '"' + space2;
  });

  // Remove trailing commas again after fixing values (in case new commas were introduced)
  fixed = fixed.replace(/,(\s*[}\]])/g, '$1');

  return fixed;
}

function mergeStandaloneArrays(obj: string): string {
  // Find all standalone arrays and the first array key to merge them into
  const standaloneArrayPattern = /,(\s*)\[([^\]]+)](\s*)(?=[,}])/g;
  const standaloneArrays: Array<{ match: string; content: string }> = [];

  let match;
  while ((match = standaloneArrayPattern.exec(obj)) !== null) {
    standaloneArrays.push({
      match: match[0],
      content: match[2],
    });
  }

  if (standaloneArrays.length === 0) {
    return obj;
  }

  // Find the first array key-value pair to merge into
  const arrayKeyPattern = /"([^"]+)"\s*:\s*\[([^\]]*)]/;
  const arrayKeyMatch = obj.match(arrayKeyPattern);

  if (!arrayKeyMatch || standaloneArrays.length === 1) {
    // No existing array to merge into, or only one standalone array -> convert to key-value pairs
    return obj.replace(/,(\s*)\[([^\]]+)](\s*)(?=[,}])/g, (_match, space1, arrayContent, space2) => {
      const items = arrayContent.split(',').map((item: string) => item.trim().replace(/^["']|["']$/g, ''));
      if (items.length > 0 && items[0]) {
        const key = items[0].replace(/[^a-zA-Z0-9_]/g, '');
        return ',' + space1 + '"' + key + '": ""' + space2;
      }
      return '';
    });
  }

  // Multiple standalone arrays -> merge into the first array
  const existingArrayContent = arrayKeyMatch[2];
  const mergedItems: string[] = [];

  // Add existing array items
  if (existingArrayContent.trim()) {
    mergedItems.push(...existingArrayContent.split(',').map((item) => item.trim()));
  }

  // Add items from standalone arrays
  for (const standaloneArray of standaloneArrays) {
    const items = standaloneArray.content.split(',').map((item) => item.trim());
    mergedItems.push(...items);
  }

  // Create the merged array string
  const mergedArrayString = '[' + mergedItems.join(', ') + ']';

  // Replace the original array with the merged one
  let result = obj.replace(arrayKeyPattern, `"${arrayKeyMatch[1]}": ${mergedArrayString}`);

  // Remove all standalone arrays
  result = result.replace(/,(\s*)\[([^\]]+)](\s*)(?=[,}])/g, '');

  return result;
}

function tryFixMalformedArray(content: string): string | null {
  const trimmed = content.trim();

  if (!trimmed.startsWith('[')) {
    return null;
  }

  if (trimmed === '[' || trimmed === '[-' || /^\[\s*-\s*$/.test(trimmed)) {
    return '[]';
  }

  // Try fixing unquoted array values first
  const fixedUnquoted = fixUnquotedArrayValues(trimmed);
  if (tryParseJson(fixedUnquoted)) {
    return fixedUnquoted;
  }

  const advancedResult = tryAdvancedArrayFix(trimmed);
  if (advancedResult) {
    return advancedResult;
  }

  const parseResult = parseArrayStructure(trimmed);

  if (parseResult.arrayDepth === 0 && parseResult.objectDepth === 1) {
    return fixIncompleteObjectInArray(trimmed);
  }

  if (parseResult.lastValidObjectEnd > -1 && parseResult.arrayDepth === 1) {
    const fixed = trimmed.slice(0, parseResult.lastValidObjectEnd + 1) + ']';
    if (tryParseJson(fixed)) {
      return fixed;
    }
    return tryFixIncompleteObject(trimmed);
  }

  const incompleteResult = tryFixIncompleteObject(trimmed);
  if (incompleteResult) {
    return incompleteResult;
  }

  if (parseResult.arrayDepth === 1) {
    return tryCloseArray(trimmed);
  }

  return null;
}

type ArrayParseResult = {
  arrayDepth: number;
  objectDepth: number;
  lastValidObjectEnd: number;
};

function parseArrayStructure(trimmed: string): ArrayParseResult {
  let arrayDepth = 0;
  let objectDepth = 0;
  let inString = false;
  let escaped = false;
  let lastValidObjectEnd = -1;

  for (let i = 0; i < trimmed.length; i++) {
    const char = trimmed[i];

    if (escaped) {
      escaped = false;
      continue;
    }

    if (char === '\\') {
      escaped = true;
      continue;
    }

    if (char === '"' && !escaped) {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === '[') {
        arrayDepth++;
      } else if (char === ']') {
        arrayDepth--;
        if (arrayDepth === 0 && objectDepth === 0) {
          return { arrayDepth: 0, objectDepth: 0, lastValidObjectEnd: i };
        }
      } else if (char === '{') {
        objectDepth++;
      } else if (char === '}') {
        objectDepth--;
        if (objectDepth === 0 && arrayDepth === 1) {
          lastValidObjectEnd = i;
        }
      }
    }
  }

  return { arrayDepth, objectDepth, lastValidObjectEnd };
}

function fixIncompleteObjectInArray(trimmed: string): string | null {
  const closingBracketIndex = trimmed.lastIndexOf(']');
  if (closingBracketIndex > -1) {
    const fixed = trimmed.slice(0, closingBracketIndex) + '}' + trimmed.slice(closingBracketIndex);
    return tryParseJson(fixed) ? fixed : null;
  }
  return null;
}

function tryCloseArray(trimmed: string): string | null {
  const fixedWithCommas = tryFixMissingCommas(trimmed);
  const fixed = fixedWithCommas + ']';

  if (tryParseJson(fixed)) {
    return fixed;
  }

  const simpleFix = trimmed + ']';
  return tryParseJson(simpleFix) ? simpleFix : null;
}

function tryFixMissingCommas(content: string): string {
  const trimmed = content.trim();

  if (!trimmed.startsWith('[')) {
    return trimmed;
  }

  let fixed = trimmed.slice(1);

  // Fix missing commas between consecutive quoted strings
  fixed = fixed.replace(/"(\s+)"/g, '", "');
  // Fix missing commas between quoted string and number
  fixed = fixed.replace(/"(\s+)(\d)/g, '", $2');
  // Fix missing commas between number and quoted string
  fixed = fixed.replace(/(\d)(\s+)"/g, '$1, "');
  // Fix missing commas between consecutive numbers
  fixed = fixed.replace(/(\d)(\s+)(\d)/g, '$1, $3');

  return '[' + fixed;
}

function fixUnquotedArrayValues(content: string): string {
  if (!content.startsWith('[')) {
    return content;
  }

  let fixed = content;

  // Fix unquoted values in arrays that are not valid numbers
  fixed = fixed.replace(/,(\s*)([^",\[\]{}]+?)(\s*)([,\]])/g, (match, space1, value, space2, ending) => {
    const trimmedValue = value.trim();
    const lowerValue = trimmedValue.toLowerCase();

    // Check if it's a number (including leading decimal, trailing decimal, and incomplete scientific notation)
    if (/^-?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d*)?$/.test(trimmedValue)) {
      // Fix leading decimal point by adding 0
      let fixedNumber = trimmedValue;
      if (fixedNumber.startsWith('.')) {
        fixedNumber = '0' + fixedNumber;
      } else if (fixedNumber.startsWith('-.')) {
        fixedNumber = '-0' + fixedNumber.slice(1);
      }
      // Fix trailing decimal point by removing it
      if (fixedNumber.endsWith('.')) {
        fixedNumber = fixedNumber.slice(0, -1);
      }
      // Fix incomplete scientific notation (e.g., "1e" -> "1")
      if (/[eE][+-]?$/.test(fixedNumber)) {
        fixedNumber = fixedNumber.replace(/[eE][+-]?$/, '');
      }
      return ',' + space1 + fixedNumber + space2 + ending;
    }

    // Handle boolean and null values
    if (lowerValue === 'true' || lowerValue === 'false' || lowerValue === 'null') {
      return ',' + space1 + lowerValue + space2 + ending;
    }

    // Skip if already quoted
    if (trimmedValue.startsWith('"')) {
      return match;
    }

    // Quote the value
    return ',' + space1 + '"' + trimmedValue + '"' + space2 + ending;
  });

  // Handle the first element (no leading comma)
  fixed = fixed.replace(/^\[(\s*)([^",\[\]{}]+?)(\s*)([,\]])/g, (match, space1, value, space2, ending) => {
    const trimmedValue = value.trim();
    const lowerValue = trimmedValue.toLowerCase();

    // Check if it's a number (including leading decimal, trailing decimal, and incomplete scientific notation)
    if (/^-?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d*)?$/.test(trimmedValue)) {
      // Fix leading decimal point by adding 0
      let fixedNumber = trimmedValue;
      if (fixedNumber.startsWith('.')) {
        fixedNumber = '0' + fixedNumber;
      } else if (fixedNumber.startsWith('-.')) {
        fixedNumber = '-0' + fixedNumber.slice(1);
      }
      // Fix trailing decimal point by removing it
      if (fixedNumber.endsWith('.')) {
        fixedNumber = fixedNumber.slice(0, -1);
      }
      // Fix incomplete scientific notation (e.g., "1e" -> "1")
      if (/[eE][+-]?$/.test(fixedNumber)) {
        fixedNumber = fixedNumber.replace(/[eE][+-]?$/, '');
      }
      return '[' + space1 + fixedNumber + space2 + ending;
    }

    // Handle boolean and null values
    if (lowerValue === 'true' || lowerValue === 'false' || lowerValue === 'null') {
      return '[' + space1 + lowerValue + space2 + ending;
    }

    // Skip if already quoted
    if (trimmedValue.startsWith('"')) {
      return match;
    }

    // Quote the value
    return '[' + space1 + '"' + trimmedValue + '"' + space2 + ending;
  });

  return fixed;
}

function tryFixIncompleteObjectStandalone(content: string): string | null {
  const trimmed = content.trim();

  if (!trimmed.startsWith('{')) {
    return null;
  }

  if (trimmed === '{') {
    return '{}';
  }

  const braceDepth = countUnclosedBraces(trimmed);
  let fixed = trimmed;

  for (let i = 0; i < braceDepth; i++) {
    fixed += '}';
  }

  fixed = fixUnquotedKeysAndValues(fixed);
  return tryParseJson(fixed) ? fixed : null;
}

function countUnclosedBraces(trimmed: string): number {
  let braceDepth = 0;
  let inString = false;
  let escaped = false;

  for (let i = 0; i < trimmed.length; i++) {
    const char = trimmed[i];

    if (escaped) {
      escaped = false;
      continue;
    }

    if (char === '\\') {
      escaped = true;
      continue;
    }

    if (char === '"' && !escaped) {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === '{') {
        braceDepth++;
      } else if (char === '}') {
        braceDepth--;
      }
    }
  }

  return braceDepth;
}

function tryFixIncompleteObject(content: string): string | null {
  const trimmed = content.trim();

  if (!trimmed.startsWith('[')) {
    return null;
  }

  const parseResult = parseIncompleteObjectStructure(trimmed);

  if (parseResult.arrayDepth === 0 && parseResult.objectDepth === 1) {
    const withoutClosingBracket = trimmed.slice(0, -1);
    const fixed = withoutClosingBracket + '}]';
    return tryParseJson(fixed) ? fixed : null;
  }

  if (parseResult.arrayDepth === 1 && parseResult.objectDepth === 1) {
    const lastChar = trimmed[parseResult.lastValidChar];
    if (lastChar === ':') {
      return null;
    }
    const fixed = trimmed + '}]';
    return tryParseJson(fixed) ? fixed : null;
  }

  return null;
}

type IncompleteObjectParseResult = {
  arrayDepth: number;
  objectDepth: number;
  lastValidChar: number;
};

function parseIncompleteObjectStructure(trimmed: string): IncompleteObjectParseResult {
  let arrayDepth = 0;
  let objectDepth = 0;
  let inString = false;
  let escaped = false;
  let lastValidChar = -1;

  for (let i = 0; i < trimmed.length; i++) {
    const char = trimmed[i];

    if (escaped) {
      escaped = false;
      continue;
    }

    if (char === '\\') {
      escaped = true;
      continue;
    }

    if (char === '"' && !escaped) {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === '[') {
        arrayDepth++;
      } else if (char === ']') {
        arrayDepth--;
      } else if (char === '{') {
        objectDepth++;
      } else if (char === '}') {
        objectDepth--;
      }

      if (char.trim() !== '') {
        lastValidChar = i;
      }
    }
  }

  return { arrayDepth, objectDepth, lastValidChar };
}
