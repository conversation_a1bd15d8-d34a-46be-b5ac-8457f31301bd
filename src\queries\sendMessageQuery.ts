import { FileUpload, IAction } from '@/components/Bot';
import { sendRequest } from '@/utils/index';

export type IncomingInput = {
  question: string;
  uploads?: FileUpload[];
  overrideConfig?: Record<string, unknown>;
  socketIOClientId?: string;
  chatId?: string;
  fileName?: string; // Only for assistant
  leadEmail?: string;
  action?: IAction;
  stored?: Record<string, any>;
  fetch?: boolean;
  faqId?: string;
  useId?: string;
};

type BaseRequest = {
  apiHost?: string;
  onRequest?: (request: RequestInit) => Promise<void>;
};

export type MessageRequest = BaseRequest & {
  chatflowid?: string;
  body?: IncomingInput;
};

export type FeedbackRatingType = 'THUMBS_UP' | 'THUMBS_DOWN';

export type FeedbackInput = {
  chatId: string;
  messageId: string;
  rating: any;
  content?: string;
};

export type CreateFeedbackRequest = BaseRequest & {
  chatflowid?: string;
  body?: FeedbackInput;
};

export type UpdateFeedbackRequest = BaseRequest & {
  id: string;
  body?: Partial<FeedbackInput>;
};

export type UpsertRequest = BaseRequest & {
  chatflowid: string;
  apiHost?: string;
  formData: FormData;
};

export type LeadCaptureInput = {
  chatflowid: string;
  chatId: string;
  name?: string;
  email?: string;
  phone?: string;
};

export type GetFeedbackByMessageIdRequest = BaseRequest & {
  messageId: string;
};

export type LeadCaptureRequest = BaseRequest & {
  body: Partial<LeadCaptureInput>;
};

export const getUserFromLocalStorage = (): any | null => {
  const dataLogin = localStorage?.getItem('dataLogin');
  if (dataLogin) {
    try {
      const data = JSON?.parse(dataLogin);
      return data.accessToken;
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
      return null;
    }
  }
  return null;
};

export const sendFeedbackQuery = ({ chatflowid, apiHost = 'http://localhost:3000', body, onRequest }: CreateFeedbackRequest) =>
  sendRequest({
    method: 'POST',
    url: `${apiHost}/api/v1/feedback/${chatflowid}`,
    body,
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });

export const getFeedbackByMessageId = ({ messageId, apiHost = 'http://localhost:3000', onRequest }: GetFeedbackByMessageIdRequest) =>
  sendRequest({
    method: 'GET',
    url: `${apiHost}/api/v1/feedback/message-id/${messageId}`,
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });

export const updateFeedbackQuery = ({ id, apiHost = 'http://localhost:3000', body, onRequest }: UpdateFeedbackRequest) =>
  sendRequest({
    method: 'PUT',
    url: `${apiHost}/api/v1/feedback/${id}`,
    body,
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });

export const sendMessageQuery = ({ chatflowid, apiHost = 'http://localhost:3000', body, onRequest }: MessageRequest) => {
  return sendRequest<any>({
    method: 'POST',
    url: `${apiHost}/api/v1/prediction/${chatflowid}`,
    body,
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });
};

export const createAttachmentWithFormData = ({ chatflowid, apiHost = 'http://localhost:3000', formData, onRequest }: UpsertRequest) =>
  sendRequest({
    method: 'POST',
    url: `${apiHost}/api/v1/attachments/${chatflowid}/${formData.get('chatId')}`,
    formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
    onRequest: onRequest,
  });

export const upsertVectorStoreWithFormData = ({ chatflowid, apiHost = 'http://localhost:3000', formData, onRequest }: UpsertRequest) =>
  sendRequest({
    method: 'POST',
    url: `${apiHost}/api/v1/vector/upsert/${chatflowid}`,
    formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
    onRequest: onRequest,
  });

export const getChatbotConfig = ({ chatflowid, apiHost = 'http://localhost:3000', onRequest }: MessageRequest) =>
  sendRequest<any>({
    method: 'GET',
    url: `${apiHost}/api/v1/public-chatbotConfig/${chatflowid}`,
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });

export const isStreamAvailableQuery = ({ chatflowid, apiHost = 'http://localhost:3000', onRequest }: MessageRequest) =>
  sendRequest<any>({
    method: 'GET',
    url: `${apiHost}/api/v1/chatflows-streaming/${chatflowid}`,
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });

export const sendFileDownloadQuery = ({ apiHost = 'http://localhost:3000', body, onRequest }: MessageRequest) =>
  sendRequest<any>({
    method: 'POST',
    url: `${apiHost}/api/v1/openai-assistants-file/download`,
    body,
    type: 'blob',
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });

export const addLeadQuery = ({ apiHost = 'http://localhost:3000', body, onRequest }: LeadCaptureRequest) =>
  sendRequest<any>({
    method: 'POST',
    url: `${apiHost}/api/v1/leads/`,
    body,
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });

export const searchFaqsQuery = async ({
  chatflowid,
  apiHost = 'http://localhost:3000',
  query,
  onRequest,
}: {
  chatflowid?: string;
  apiHost?: string;
  query: string;
  onRequest?: (request: RequestInit) => Promise<void>;
}) => {
  return sendRequest<any>({
    method: 'GET',
    url: `${apiHost}/api/v1/faq/search/${chatflowid}?query=${query}`,
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });
};

export const vectorSearchFaqsQuery = async ({
  chatflowid,
  apiHost = 'http://localhost:3000',
  query,
  onRequest,
}: {
  chatflowid: string;
  apiHost?: string;
  query: string;
  onRequest?: (request: RequestInit) => Promise<void>;
}) => {
  return sendRequest<any>({
    method: 'GET',
    url: `${apiHost}/api/v1/faq/vectorsearch/${chatflowid}?query=${query}`,
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });
};

export const getFileContent = async ({
  apiHost = 'https://s3-explorer.cmcts1.studio.ai.vn',
  path,
  onRequest,
}: {
  apiHost?: string;
  path: string;
  onRequest?: (request: RequestInit) => Promise<void>;
}) => {
  return sendRequest<any>({
    method: 'GET',
    url: `${apiHost}/api/read-text?path=${path}`,
    onRequest: onRequest,
    headers: {
      Authorization: `Bearer ${getUserFromLocalStorage()}`,
    },
  });
};
