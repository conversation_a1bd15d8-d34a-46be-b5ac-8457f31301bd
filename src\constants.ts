import type { BubbleProps } from './features/bubble';

export const defaultBotProps: BubbleProps = {
  chatflowid: '',
  apiHost: undefined,
  chatwootUrl: undefined,
  onRequest: undefined,
  chatflowConfig: undefined,
  theme: undefined,
  observersConfig: undefined,
  roomIds: {},
  isUseFAQ: false, // Default to false, popup mode will override to true
  filePath: '',
  suggestQuestions: '',
  externalData: '',
  callBackIcon: '',
  callBackFunction: undefined,
};
