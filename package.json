{"name": "cmcts-c-agent-embedding", "version": "1.0.60-vib", "description": "Javascript library to display flowise chatbot on your website", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "cross-env NODE_ENV=development rollup --watch --config rollup.config.js", "build": "cross-env NODE_ENV=production rollup --config rollup.config.js", "type-check": "tsc --noEmit", "lint": "eslint \"src/**/*.ts*\"", "lint-fix": "eslint --fix \"src/**/*.ts*\"", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,md,mdx}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,md,mdx}\"", "start": "node server.js", "build-and-publish": "yarn build && npm publish"}, "license": "MIT", "dependencies": {"@babel/core": "^7.22.1", "@microsoft/fetch-event-source": "^2.0.1", "@ts-stack/markdown": "^1.4.0", "@types/pdfjs-dist": "^2.10.378", "axios": "^1.7.7", "c-agent-embedding": "^1.0.4", "c-agent-embedding-react": "^1.0.1", "cors": "^2.8.5", "cross-env": "^7.0.3", "device-detector-js": "^3.0.3", "dotenv": "^16.4.5", "express": "^4.21.1", "form-data": "^4.0.1", "lodash": "^4.17.21", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "pdfjs-dist": "3.11.174", "prettier": "^3.4.2", "solid-element": "1.7.0", "solid-js": "1.7.1", "zod": "^3.22.4"}, "devDependencies": {"@babel/preset-typescript": "7.21.4", "@rollup/plugin-babel": "6.0.3", "@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "15.0.1", "@rollup/plugin-terser": "0.4.0", "@rollup/plugin-typescript": "11.0.0", "@tailwindcss/typography": "^0.5.10", "@types/lodash": "^4.14.195", "@types/node": "18.15.11", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "autoprefixer": "10.4.14", "babel-plugin-lodash": "^3.3.4", "babel-preset-solid": "1.7.1", "eslint": "^8.24.0", "eslint-config-next": "13.2.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.26.1", "eslint-plugin-solid": "0.12.0", "postcss": "8.4.21", "react": "18.2.0", "rollup": "3.23.0", "rollup-plugin-livereload": "2.0.5", "rollup-plugin-postcss": "4.0.2", "rollup-plugin-serve": "2.0.2", "rollup-plugin-typescript-paths": "1.4.0", "rollup-plugin-uglify": "^6.0.4", "tailwindcss": "3.3.1", "typescript": "5.0.3", "uuid": "^9.0.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}