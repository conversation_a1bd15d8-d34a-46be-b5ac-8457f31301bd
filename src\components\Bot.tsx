import { Avatar } from '@/components/avatars/Avatar';
import { FollowUpPromptBubble } from '@/components/bubbles/FollowUpPromptBubble';
import { LeadCaptureBubble } from '@/components/bubbles/LeadCaptureBubble';
import { DeleteButton, FullScreenButton, SendButton } from '@/components/buttons/SendButton';
import { FilePreview } from '@/components/inputs/textInput/components/FilePreview';
import {
  BotMessageTheme,
  DateTimeToggleTheme,
  DisclaimerPopUpTheme,
  FeedbackTheme,
  FooterTheme,
  TextInputTheme,
  UserMessageTheme,
} from '@/features/bubble/types';
import { DisclaimerPopup, Popup } from '@/features/popup';
import {
  createAttachmentWithFormData,
  FeedbackRatingType,
  getChatbotConfig,
  getFileContent,
  getUserFromLocalStorage,
  IncomingInput,
  isStreamAvailableQuery,
  sendMessageQuery,
  upsertVectorStoreWithFormData,
} from '@/queries/sendMessageQuery';
import {
  checkAndInitializeAuth,
  getCookie,
  getLocalStorageChatflow,
  reloadAndMapUserInfo,
  removeLocalStorageChatHistory,
  setCookie,
  setLocalStorageChatflow,
} from '@/utils';
import { cancelAudioRecording, startAudioRecording, stopAudioRecording } from '@/utils/audioRecording';
import { EventStreamContentType, fetchEventSource } from '@microsoft/fetch-event-source';
import axios from 'axios';
import { cloneDeep } from 'lodash';
import { createEffect, createMemo, createSignal, For, mergeProps, on, onMount, Show } from 'solid-js';
import { v4 as uuidv4, v4 } from 'uuid';
import { Badge } from './Badge';
import { Toast } from './Toast';
import { BotBubble } from './bubbles/BotBubble';
import { GuestBubble } from './bubbles/GuestBubble';
import { LoadingBubble } from './bubbles/LoadingBubble';
import { StarterPromptBubble } from './bubbles/StarterPromptBubble';
import { CancelButton } from './buttons/CancelButton';
import { CircleDotIcon, SparklesIcon, TrashIcon } from './icons';
import { PreviewImage } from './image/PreviewImage';
import { TextInput } from './inputs/textInput';

export type FileEvent<T = EventTarget> = {
  target: T;
};

export type FormEvent<T = EventTarget> = {
  preventDefault: () => void;
  currentTarget: T;
};

type IUploadConstraits = {
  fileTypes: string[];
  maxUploadSize: number;
};

export type UploadsConfig = {
  imgUploadSizeAndTypes: IUploadConstraits[];
  fileUploadSizeAndTypes: IUploadConstraits[];
  isImageUploadAllowed: boolean;
  isSpeechToTextEnabled: boolean;
  isRAGFileUploadAllowed: boolean;
};

type FilePreviewData = string | ArrayBuffer;

type FilePreview = {
  data: FilePreviewData;
  mime: string;
  name: string;
  preview: string;
  type: string;
};

type UserInfo = {
  id?: any;
  name?: string;
  email?: string;
  phoneNumber?: string;
  [key: string]: any;
};

type messageType = 'apiMessage' | 'userMessage' | 'usermessagewaiting' | 'leadCaptureMessage';

export type IAgentReasoning = {
  agentName?: string;
  messages?: string[];
  usedTools?: any[];
  artifacts?: FileUpload[];
  sourceDocuments?: any[];
  instructions?: string;
  nextAgent?: string;
};

export type IAction = {
  id?: string;
  elements?: Array<{
    type: string;
    label: string;
  }>;
  mapping?: {
    approve: string;
    reject: string;
    toolCalls: any[];
  };
};

export type FileUpload = Omit<FilePreview, 'preview'>;

export type MessageType = {
  messageId?: string;
  message: string;
  type: messageType;
  sourceDocuments?: any;
  fileAnnotations?: any;
  fileUploads?: Partial<FileUpload>[];
  artifacts?: Partial<FileUpload>[];
  agentReasoning?: IAgentReasoning[];
  usedTools?: any[];
  action?: IAction | null;
  rating?: FeedbackRatingType;
  id?: string;
  followUpPrompts?: string;
  dateTime?: string;
};

type IUploads = {
  data: FilePreviewData;
  type: string;
  name: string;
  mime: string;
  file?: any;
}[];

type observerConfigType = (accessor: string | boolean | object | MessageType[]) => void;
export type observersConfigType = Record<'observeUserInput' | 'observeLoading' | 'observeMessages', observerConfigType>;

export type BotProps = {
  chatflowid: string;
  apiHost?: string;
  onRequest?: (request: RequestInit) => Promise<void>;
  chatflowConfig?: Record<string, unknown>;
  welcomeMessage?: string;
  secondMessage?: string;
  errorMessage?: string;
  botMessage?: BotMessageTheme;
  userMessage?: UserMessageTheme;
  textInput?: TextInputTheme;
  feedback?: FeedbackTheme;
  poweredByTextColor?: string;
  badgeBackgroundColor?: string;
  bubbleBackgroundColor?: string;
  bubbleTextColor?: string;
  showTitle?: boolean;
  showAgentMessages?: boolean;
  title?: string;
  titleAvatarSrc?: string;
  fontSize?: number;
  isFullPage?: boolean;
  footer?: FooterTheme;
  sourceDocsTitle?: string;
  observersConfig?: observersConfigType;
  starterPrompts?: string[] | Record<string, { prompt: string }>;
  starterPromptFontSize?: number;
  clearChatOnReload?: boolean;
  disclaimer?: DisclaimerPopUpTheme;
  dateTimeToggle?: DateTimeToggleTheme;
  renderHTML?: boolean;
  chatwootUrl?: string;
  roomIds?: Record<string, string>;
  isUseFAQ?: boolean;
  filePath?: string;
  suggestQuestions?: string;
  externalData?: string;
  callBackFunction?: (e: any) => void;
  callBackIcon?: string;
  onToggleFullScreen?: () => void;
  isFullScreen?: boolean;
};

export type LeadsConfig = {
  status: boolean;
  title?: string;
  name?: boolean;
  email?: boolean;
  phone?: boolean;
  successMessage?: string;
};

const defaultWelcomeMessage = 'Xin chào tôi có thể giúp gì cho bạn?';
const statusMsg = {
  3: 'Đang xử lý...',
  10: 'Đang tải dữ liệu, vui lòng đợi...',
  40: 'Xin lỗi, hiện tại hệ thống đang quá tải, vui lòng thử lại sau.',
  50: '',
};

const defaultBackgroundColor = '#ffffff';
const defaultTextColor = '#303235';

// Helper functions for caching chatbot config
const getCachedChatbotConfig = (chatflowid: string) => {
  try {
    const cached = localStorage.getItem(`chatbotConfig_${chatflowid}`);
    return cached ? JSON.parse(cached) : null;
  } catch (error) {
    console.warn('Failed to parse cached chatbot config:', error);
    return null;
  }
};

const setCachedChatbotConfig = (chatflowid: string, config: any) => {
  try {
    localStorage.setItem(`chatbotConfig_${chatflowid}`, JSON.stringify(config));
  } catch (error) {
    console.warn('Failed to cache chatbot config:', error);
  }
};

const getWelcomeMessage = (props: { welcomeMessage?: string; chatflowid: string }) => {
  if (props.welcomeMessage) return props.welcomeMessage;
  const cachedConfig = getCachedChatbotConfig(props.chatflowid);
  if (cachedConfig?.welcomeMessage) return cachedConfig.welcomeMessage;
  return '';
};

export const Bot = (botProps: BotProps & { class?: string }) => {
  // set a default value for showTitle if not set and merge with other props

  const props = mergeProps({ showTitle: true }, botProps);
  let chatContainer: HTMLDivElement | undefined;
  let bottomSpacer: HTMLDivElement | undefined;
  let botContainer: HTMLDivElement | undefined;
  let intervalCurrentMsg: any;

  const [showScrollButton, setShowScrollButton] = createSignal(false);
  const [userInput, setUserInput] = createSignal('');
  const [loading, setLoading] = createSignal(false);
  const [sourcePopupOpen, setSourcePopupOpen] = createSignal(false);
  const [sourcePopupSrc, setSourcePopupSrc] = createSignal({});
  const [currentStatusMsg, setCurrentStatusMsg] = createSignal('');
  const [isNetworkError, setIsNetworkError] = createSignal(false);
  const [networkErrorMessage, setNetworkErrorMessage] = createSignal('');
  const [user, setUser] = createSignal({ id: '', username: '', email: '' });

  // Timer for streaming response
  const [streamingStartTime, setStreamingStartTime] = createSignal<number | null>(null);
  const [elapsedStreamingTime, setElapsedStreamingTime] = createSignal(0);
  let streamingTimerInterval: any;
  const [messages, setMessages] = createSignal<MessageType[]>(
    [
      {
        message: getWelcomeMessage(props),
        type: 'apiMessage' as messageType,
      },
      ...(props.secondMessage ? [{ message: props.secondMessage, type: 'apiMessage' as messageType }] : []),
    ],
    { equals: false },
  );

  const [isChatFlowAvailableToStream, setIsChatFlowAvailableToStream] = createSignal(false);
  const [chatId, setChatId] = createSignal('');
  const [isMessageStopping, setIsMessageStopping] = createSignal(false);
  const [starterPrompts, setStarterPrompts] = createSignal<string[]>([], { equals: false });
  const [chatFeedbackStatus, setChatFeedbackStatus] = createSignal<boolean>(false);
  const [fullFileUpload, setFullFileUpload] = createSignal<boolean>(false);
  const [uploadsConfig, setUploadsConfig] = createSignal<UploadsConfig>();
  const [leadsConfig, setLeadsConfig] = createSignal<LeadsConfig>();
  const [isLeadSaved, setIsLeadSaved] = createSignal(false);
  const [leadEmail, setLeadEmail] = createSignal('');
  const [disclaimerPopupOpen, setDisclaimerPopupOpen] = createSignal(false);
  const [disableInput, setDisableInput] = createSignal(false);
  const [fileSummary, setFileSummary] = createSignal('');
  const [fileContent, setFileContent] = createSignal('');
  const [suggestQuestion, setSuggestQuestion] = createSignal([]);
  const [previewImage, setPreivewImage] = createSignal(null);

  // drag & drop file input
  // TODO: fix this type
  const [previews, setPreviews] = createSignal<FilePreview[]>([]);

  // audio recording
  const [elapsedTime, setElapsedTime] = createSignal('00:00');
  const [isRecording, setIsRecording] = createSignal(false);
  const [recordingNotSupported, setRecordingNotSupported] = createSignal(false);
  const [isLoadingRecording, setIsLoadingRecording] = createSignal(false);

  // follow-up prompts
  const [followUpPromptsStatus, setFollowUpPromptsStatus] = createSignal<boolean>(false);
  const [followUpPrompts, setFollowUpPrompts] = createSignal<string[]>([]);

  // drag & drop
  const [isDragActive, setIsDragActive] = createSignal(false);
  const [uploadedFiles, setUploadedFiles] = createSignal<{ file: File; type: string }[]>([]);

  createMemo(() => {
    const customerId = (props.chatflowConfig?.vars as any)?.customerId;
    setChatId(customerId ? `${customerId.toString()}+${uuidv4()}` : uuidv4());
  });

  onMount(() => {
    if (botProps?.observersConfig) {
      const { observeUserInput, observeLoading, observeMessages } = botProps.observersConfig;
      typeof observeUserInput === 'function' &&
        // eslint-disable-next-line solid/reactivity
        createMemo(() => {
          observeUserInput(userInput());
        });
      typeof observeLoading === 'function' &&
        // eslint-disable-next-line solid/reactivity
        createMemo(() => {
          observeLoading(loading());
        });
      typeof observeMessages === 'function' &&
        // eslint-disable-next-line solid/reactivity
        createMemo(() => {
          observeMessages(messages());
        });
    }

    if (!bottomSpacer) return;
    setTimeout(() => {
      chatContainer?.scrollTo(0, chatContainer.scrollHeight);
    }, 50);
  });

  createEffect(() => {
    const currentMessages = messages();

    currentMessages?.forEach((message) => {
      message.agentReasoning?.forEach((reason: any) => {
        if (reason?.state) {
          const { name, phone_number, email, id } = reason.state;
          const userInfo: Record<string, string> = {};
          if (name && name !== 'None') {
            userInfo.name = name;
          }
          if (phone_number && phone_number !== 'None') {
            userInfo.phoneNumber = phone_number;
          }
          if (email && email !== 'None') {
            userInfo.email = email;
          }
          if (id && id !== 'None') {
            userInfo.id = id;
          }
          if (Object.keys(userInfo).length > 0) {
            localStorage.setItem('chatwoot:userInfo', JSON.stringify(userInfo));
            localStorage.setItem('cmcts:userInfo', JSON.stringify(userInfo));
          }
        }
      });
    });
  });

  const scrollToBottom = () => {
    setTimeout(() => {
      chatContainer?.scrollTo(0, chatContainer.scrollHeight);
    }, 50);
  };

  // Streaming timer functions
  const startStreamingTimer = () => {
    const startTime = Date.now();
    setStreamingStartTime(startTime);
    setElapsedStreamingTime(0);

    streamingTimerInterval = setInterval(() => {
      const elapsed = (Date.now() - startTime) / 1000;
      setElapsedStreamingTime(elapsed);
    }, 100); // Update every 100ms for 0.1 second precision
  };

  const stopStreamingTimer = () => {
    if (streamingTimerInterval) {
      clearInterval(streamingTimerInterval);
      streamingTimerInterval = null;
    }
    setStreamingStartTime(null);
    setElapsedStreamingTime(0);
  };

  const handleClick = (e: any) => {
    if (e.target.tagName === 'IMG') {
      setPreivewImage(e.target.cloneNode(true));
    }
  };

  /**
   * Add each chat message into localStorage
   */
  const addChatMessage = (allMessage: MessageType[]) => {
    const messages = allMessage.map((item) => {
      if (item.fileUploads) {
        const fileUploads = item?.fileUploads.map((file) => ({
          type: file.type,
          name: file.name,
          mime: file.mime,
        }));
        return { ...item, fileUploads };
      }
      return item;
    });
    setLocalStorageChatflow(props.chatflowid, chatId(), { chatHistory: messages });
  };

  // Define the audioRef
  let audioRef: HTMLAudioElement | undefined;
  // CDN link for default receive sound
  const defaultReceiveSound = 'https://cdn.jsdelivr.net/gh/FlowiseAI/FlowiseChatEmbed@latest/src/assets/receive_message.mp3';
  const playReceiveSound = () => {
    if (props.textInput?.receiveMessageSound) {
      let audioSrc = defaultReceiveSound;
      if (props.textInput?.receiveSoundLocation) {
        audioSrc = props.textInput?.receiveSoundLocation;
      }
      audioRef = new Audio(audioSrc);
      audioRef.play();
    }
  };

  let hasSoundPlayed = false;

  const updateLastMessage = (text: string) => {
    setMessages((prevMessages) => {
      const allMessages = [...cloneDeep(prevMessages)];
      if (allMessages[allMessages.length - 1].type === 'userMessage') return allMessages;
      if (!text) return allMessages;
      allMessages[allMessages.length - 1].message += text;
      allMessages[allMessages.length - 1].rating = undefined;
      allMessages[allMessages.length - 1].dateTime = new Date().toISOString();
      if (!hasSoundPlayed) {
        playReceiveSound();
        hasSoundPlayed = true;
      }
      addChatMessage(allMessages);
      return allMessages;
    });
  };

  const updateErrorMessage = (errorMessage: string) => {
    setMessages((prevMessages) => {
      const allMessages = [...cloneDeep(prevMessages)];
      allMessages.push({ message: props.errorMessage || 'Đã xảy ra lỗi, vui lòng thử lại sau.', type: 'apiMessage' });
      addChatMessage(allMessages);
      return allMessages;
    });
  };

  const updateLastMessageSourceDocuments = (sourceDocuments: any) => {
    setMessages((data) => {
      const updated = data.map((item, i) => {
        if (i === data.length - 1) {
          return { ...item, sourceDocuments };
        }
        return item;
      });
      addChatMessage(updated);
      return [...updated];
    });
  };

  const updateLastMessageUsedTools = (usedTools: any[]) => {
    setMessages((prevMessages) => {
      const allMessages = [...cloneDeep(prevMessages)];
      if (allMessages[allMessages.length - 1].type === 'userMessage') return allMessages;
      allMessages[allMessages.length - 1].usedTools = usedTools;
      addChatMessage(allMessages);
      return allMessages;
    });
  };

  const updateLastMessageFileAnnotations = (fileAnnotations: any) => {
    setMessages((prevMessages) => {
      const allMessages = [...cloneDeep(prevMessages)];
      if (allMessages[allMessages.length - 1].type === 'userMessage') return allMessages;
      allMessages[allMessages.length - 1].fileAnnotations = fileAnnotations;
      addChatMessage(allMessages);
      return allMessages;
    });
  };

  const updateLastMessageAgentReasoning = (agentReasoning: string | IAgentReasoning[]) => {
    setMessages((data) => {
      const updated = data.map((item, i) => {
        if (i === data.length - 1) {
          return { ...item, agentReasoning: typeof agentReasoning === 'string' ? JSON.parse(agentReasoning) : agentReasoning };
        }
        return item;
      });
      addChatMessage(updated);
      return [...updated];
    });
  };

  const updateLastMessageArtifacts = (artifacts: FileUpload[]) => {
    setMessages((prevMessages) => {
      const allMessages = [...cloneDeep(prevMessages)];
      if (allMessages[allMessages.length - 1].type === 'userMessage') return allMessages;
      allMessages[allMessages.length - 1].artifacts = artifacts;
      addChatMessage(allMessages);
      return allMessages;
    });
  };

  const updateLastMessageAction = (action: IAction) => {
    setMessages((data) => {
      const updated = data.map((item, i) => {
        if (i === data.length - 1) {
          return { ...item, action: typeof action === 'string' ? JSON.parse(action) : action };
        }
        return item;
      });
      addChatMessage(updated);
      return [...updated];
    });
  };

  const clearPreviews = () => {
    // Revoke the data uris to avoid memory leaks
    previews().forEach((file) => URL.revokeObjectURL(file.preview));
    setPreviews([]);
  };

  // Handle errors
  const handleError = (message = 'Oops! There seems to be an error. Please try again.') => {
    setMessages((prevMessages) => {
      const messages: MessageType[] = [...prevMessages, { message: props.errorMessage || message, type: 'apiMessage' }];
      addChatMessage(messages);
      return messages;
    });
    setLoading(false);
    setDisableInput(false);
    setUserInput('');
    setUploadedFiles([]);
    scrollToBottom();
  };

  const handleDisclaimerAccept = () => {
    setDisclaimerPopupOpen(false); // Close the disclaimer popup
    setCookie('chatbotDisclaimer', 'true', 365); // Disclaimer accepted
  };

  const promptClick = (prompt: string) => {
    handleSubmit(prompt);
  };

  const followUpPromptClick = (prompt: string) => {
    setFollowUpPrompts([]);
    handleSubmit(prompt);
  };

  const updateMetadata = (data: any, input: string) => {
    if (data.chatId) {
      setChatId(data.chatId);
    }

    // set message id that is needed for feedback
    if (data.chatMessageId) {
      setMessages((prevMessages) => {
        const allMessages = [...cloneDeep(prevMessages)];
        if (allMessages[allMessages.length - 1].type === 'apiMessage') {
          allMessages[allMessages.length - 1].messageId = data.chatMessageId;
        }
        addChatMessage(allMessages);
        return allMessages;
      });
    }

    if (input === '' && data.question) {
      // the response contains the question even if it was in an audio format
      // so if input is empty but the response contains the question, update the user message to show the question
      setMessages((prevMessages) => {
        const allMessages = [...cloneDeep(prevMessages)];
        if (allMessages[allMessages.length - 2].type === 'apiMessage') return allMessages;
        allMessages[allMessages.length - 2].message = data.question;
        addChatMessage(allMessages);
        return allMessages;
      });
    }

    if (data.followUpPrompts) {
      setMessages((prevMessages) => {
        const allMessages = [...cloneDeep(prevMessages)];
        if (allMessages[allMessages.length - 1].type === 'userMessage') return allMessages;
        allMessages[allMessages.length - 1].followUpPrompts = data.followUpPrompts;
        addChatMessage(allMessages);
        return allMessages;
      });
      setFollowUpPrompts(JSON.parse(data.followUpPrompts));
    }
  };

  const fetchResponseFromEventStream = async (chatflowid: string, params: any) => {
    const chatId = params.chatId;
    const input = params.question;
    params.streaming = true;

    let retryCount = 0;

    void fetchEventSource(`${props.apiHost}/api/v1/prediction/${chatflowid}`, {
      openWhenHidden: true,
      method: 'POST',
      body: JSON.stringify(params),
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${getUserFromLocalStorage()}`,
      },

      async onopen(response) {
        setCurrentStatusMsg('Đang xử lý...');
        if (response.ok && response.headers.get('content-type')?.startsWith(EventStreamContentType)) {
          return; // everything's good
        } else if (response.status === 429) {
          const errMessage = (await response.text()) ?? 'Too many requests. Please try again later.';
          handleError(errMessage);
          throw new Error(errMessage);
        } else if (response.status === 403) {
          const errMessage = (await response.text()) ?? 'Unauthorized';
          handleError(errMessage);
          throw new Error(errMessage);
        } else if (response.status === 401) {
          const errMessage = (await response.text()) ?? 'Unauthenticated';
          handleError(errMessage);
          throw new Error(errMessage);
        } else {
          throw new Error();
        }
      },
      async onmessage(ev) {
        const payload = JSON.parse(ev.data);
        if (disableInput() && payload.event !== 'start') {
          setCurrentStatusMsg('');
          setDisableInput(false);
          setUserInput('');
          setUploadedFiles([]);
        }

        switch (payload.event) {
          case 'start':
            setMessages((prevMessages) => [...prevMessages, { message: '', type: 'apiMessage' }]);
            break;
          case 'recheck':
            setCurrentStatusMsg('Rechecking...');
            break;
          case 'token':
            clearInterval(intervalCurrentMsg);
            setCurrentStatusMsg('');
            updateLastMessage(payload.data);
            break;
          case 'sourceDocuments':
            updateLastMessageSourceDocuments(payload.data);
            break;
          case 'usedTools':
            updateLastMessageUsedTools(payload.data);
            break;
          case 'fileAnnotations':
            updateLastMessageFileAnnotations(payload.data);
            break;
          case 'agentReasoning':
            updateLastMessageAgentReasoning(payload.data);
            break;
          case 'action':
            updateLastMessageAction(payload.data);
            break;
          case 'artifacts':
            updateLastMessageArtifacts(payload.data);
            break;
          case 'metadata':
            updateMetadata(payload.data, input);
            break;
          case 'error':
            updateErrorMessage(payload.data);
            break;
          case 'abort':
            abortMessage();
            closeResponse();
            break;
          case 'end':
            setLocalStorageChatflow(chatflowid, chatId);
            closeResponse();
            break;
        }
      },
      async onclose() {
        closeResponse();
        setCurrentStatusMsg('');
        stopStreamingTimer(); // Stop timer when connection closes
      },
      onerror(err) {
        console.error('EventSource Error: ', err);
        closeResponse();

        retryCount++;

        if (retryCount >= 3) {
          throw err;
        }

        return 1000;
      },
    });
  };

  const closeResponse = () => {
    setLoading(false);
    setDisableInput(false);
    setUploadedFiles([]);
    hasSoundPlayed = false;
    stopStreamingTimer(); // Stop the streaming timer
    setTimeout(() => {
      scrollToBottom();
    }, 100);
  };

  const abortMessage = () => {
    setIsMessageStopping(false);
    setMessages((prevMessages) => {
      const allMessages = [...cloneDeep(prevMessages)];
      if (allMessages[allMessages.length - 1].type === 'userMessage') return allMessages;
      const lastAgentReasoning = allMessages[allMessages.length - 1].agentReasoning;
      if (lastAgentReasoning && lastAgentReasoning.length > 0) {
        allMessages[allMessages.length - 1].agentReasoning = lastAgentReasoning.filter((reasoning) => !reasoning.nextAgent);
      }
      return allMessages;
    });
  };

  const handleFileUploads = async (uploads: IUploads) => {
    if (!uploadedFiles().length) return uploads;

    const filesWithFullUploadType = uploadedFiles().filter((file) => file.type === 'file:full');

    if (filesWithFullUploadType.length > 0) {
      const formData = new FormData();
      for (const file of filesWithFullUploadType) {
        formData.append('files', file.file);
      }
      formData.append('chatId', chatId());

      const response = await createAttachmentWithFormData({
        chatflowid: props.chatflowid,
        apiHost: props.apiHost,
        formData: formData,
      });

      if (!response.data) {
        throw new Error('Unable to upload documents');
      } else {
        const data = response.data as any;
        for (const extractedFileData of data) {
          const content = extractedFileData.content;
          const fileName = extractedFileData.name;

          // find matching name in previews and replace data with content
          const uploadIndex = uploads.findIndex((upload) => upload.name === fileName);
          console.log('🚀 ~ handleFileUploads ~ uploads:', uploads);
          if (uploadIndex !== -1) {
            uploads[uploadIndex] = {
              ...uploads[uploadIndex],
              data: content,
              name: fileName,
              type: 'file:full',
              file: uploadedFiles()[uploadIndex],
            };
          }
        }
      }
    }
    if (uploadsConfig()?.isRAGFileUploadAllowed) {
      const filesWithRAGUploadType = uploadedFiles().filter((file) => file.type === 'file:rag');

      if (filesWithRAGUploadType.length > 0) {
        const formData = new FormData();
        for (const file of filesWithRAGUploadType) {
          formData.append('files', file.file);
        }
        formData.append('chatId', chatId());

        const response = await upsertVectorStoreWithFormData({
          chatflowid: props.chatflowid,
          apiHost: props.apiHost,
          formData: formData,
        });

        if (!response.data) {
          throw new Error('Unable to upload documents');
        } else {
          // delay for vector store to be updated
          const delay = (delayInms: number) => {
            return new Promise((resolve) => setTimeout(resolve, delayInms));
          };
          await delay(2500); //TODO: check if embeddings can be retrieved using file name as metadata filter

          uploads = uploads.map((upload) => {
            return {
              ...upload,
              type: 'file:rag',
            };
          });
        }
      }
    }
    return uploads;
  };

  // Initialize authentication - check dataLogin or fetch from API if needed
  const initializeAuthentication = async () => {
    try {
      const authSuccess = await checkAndInitializeAuth(props.apiHost, props.onRequest, props.isFullPage);

      if (authSuccess) {
        // If authentication successful, update user state
        const dataLogin = localStorage?.getItem('dataLogin');
        if (dataLogin) {
          try {
            const data = JSON?.parse(dataLogin);
            setUser(data?.user);
            console.log('Authentication initialized successfully, user data loaded');
          } catch (parseError) {
            console.error('Failed to parse dataLogin after successful authentication', parseError);
          }
        }
      } else {
        console.log('Authentication initialization failed or user not logged in');
      }
    } catch (error) {
      console.error('Error during authentication initialization', error);
    }
  };

  // Call authentication initialization
  void initializeAuthentication();

  // Refresh authentication when popup opens (for popup mode)
  const refreshAuthenticationForPopup = async () => {
    try {
      if (!window.location.host.includes('.vib') || props.isFullPage) return;

      console.log('refreshAuthenticationForPopup: Refreshing user info for popup mode');
      const authSuccess = await reloadAndMapUserInfo(props.apiHost, props.onRequest, props.isFullPage);

      if (authSuccess) {
        // If authentication successful, update user state
        const dataLogin = localStorage?.getItem('dataLogin');
        if (dataLogin) {
          try {
            const data = JSON?.parse(dataLogin);
            setUser(data?.user);
            console.log('refreshAuthenticationForPopup: User data refreshed successfully');
          } catch (parseError) {
            console.error('refreshAuthenticationForPopup: Failed to parse dataLogin after refresh', parseError);
          }
        }
      } else {
        console.log('refreshAuthenticationForPopup: Authentication refresh failed');
        // Clear user state if authentication failed
        setUser({ id: '', username: '', email: '' });
      }
    } catch (error) {
      console.error('refreshAuthenticationForPopup: Error during authentication refresh', error);
    }
  };

  // Create a signal to track popup opening for authentication refresh
  const [shouldRefreshAuth, setShouldRefreshAuth] = createSignal(false);

  // Effect to handle authentication refresh when popup opens
  createEffect(() => {
    if (shouldRefreshAuth()) {
      void refreshAuthenticationForPopup();
      setShouldRefreshAuth(false); // Reset the signal
    }
  });

  // Expose refresh function globally for popup mode
  if (typeof window !== 'undefined') {
    (window as any).refreshChatbotAuth = () => {
      setShouldRefreshAuth(true);
    };
  }

  const handleSugestSubmit = async (value: string) => {
    await handleSubmit(value);
  };

  // Validate user authentication before allowing message submission
  const validateUserAuthentication = (): boolean => {
    try {
      if (window.location.href.includes('/login')) return false;

      if (!window.location.host.includes('.vib') || props.isFullPage) {
        return true;
      }

      // Check if dataLogin exists in localStorage
      const dataLogin = localStorage?.getItem('dataLogin');
      if (!dataLogin) {
        console.warn('validateUserAuthentication: dataLogin not found in localStorage');
        return false;
      }

      return true;
    } catch (error) {
      console.error('validateUserAuthentication: Unexpected error during validation', error);
      return false;
    }
  };

  // Handle form submission
  const handleSubmit = async (value: string, action?: IAction | undefined | null, faqId?: string) => {
    if (value.trim() === '') {
      const containsFile = previews().filter((item) => !item.mime.startsWith('image') && item.type !== 'audio').length > 0;
      if (!previews().length || (previews().length && containsFile)) {
        return;
      }
    }

    // Validate user authentication before proceeding
    if (!validateUserAuthentication()) {
      alert('Bạn cần đăng nhập để có thể gửi tin nhắn. Vui lòng đăng nhập và thử lại.');
      return;
    }

    // Always set loading and disable input when sending a message
    setLoading(true);
    setDisableInput(true);
    scrollToBottom();

    let uploads: IUploads = previews().map((item) => {
      return {
        data: item.data,
        type: item.type,
        name: item.name,
        mime: item.mime,
      };
    });

    try {
      uploads = await handleFileUploads(uploads);
    } catch (error) {
      handleError('Unable to upload documents');
      return;
    }

    clearPreviews();

    setMessages((prevMessages) => {
      const messages: MessageType[] = [...prevMessages, { message: value, type: 'userMessage', fileUploads: uploads }];
      addChatMessage(messages);
      return messages;
    });

    let deviceId = localStorage.getItem('cmcts:deviceId');
    if (!deviceId) {
      deviceId = uuidv4();
      localStorage.setItem('cmcts:deviceId', deviceId);
    }

    const body: IncomingInput = {
      question: value,
      faqId,
      chatId: chatId(),
      useId: user()?.id,
      stored: {
        deviceId,
        userInfo: (() => {
          const userInfoStr = localStorage.getItem('cmcts:userInfo');
          if (!userInfoStr) return undefined;
          try {
            return JSON.parse(userInfoStr) as UserInfo;
          } catch (e) {
            console.warn('Failed to parse userInfo from localStorage');
            return undefined;
          }
        })(),
        chatwoot: {
          conversation_id: localStorage.getItem(`chatwoot:${props.chatflowid}:conversation_id`),
        },
      },
    };

    if (uploads && uploads.length > 0) body.uploads = uploads;
    else if (!uploads?.length && fileContent() && fileSummary()) {
      let fileName = props.filePath?.split('/').pop() || '';
      fileName && !fileName.endsWith('.txt') && (fileName += '.txt');
      body.uploads = [
        {
          data: fileContent(),
          type: 'file:full',
          name: fileName,
          mime: 'text/plain',
        },
        {
          data: fileSummary(),
          type: 'file:full',
          name: fileName,
          mime: 'text/plain',
        },
      ];
    }

    if (props?.externalData) {
      body.uploads = [
        {
          data: props?.externalData,
          type: 'file:full',
          name: 'external_data.txt',
          mime: 'text/plain',
        },
      ];
    }

    if (props.chatflowConfig) body.overrideConfig = props.chatflowConfig;

    if (leadEmail()) body.leadEmail = leadEmail();

    if (action) body.action = action;

    // Check network status before making the request
    if (!navigator.onLine) {
      setIsNetworkError(true);
      setNetworkErrorMessage('Mất kết nối mạng');
      setMessages((prevMessages) => {
        const allMessages = [...cloneDeep(prevMessages)];
        allMessages.push({
          message: 'Xin lỗi, hiện tại không có kết nối mạng. Vui lòng kiểm tra lại kết nối và thử lại sau.',
          type: 'apiMessage',
          dateTime: new Date().toISOString(),
        });
        return allMessages;
      });
      setLoading(false);
      setDisableInput(false);
      setUserInput('');
      setUploadedFiles([]);
      return;
    }

    // TODO: Đoạn JSON.stringify(faqId) để fix Lỗi streaming khi chọn ảnh xong gửi luôn
    if (
      isChatFlowAvailableToStream() &&
      (!faqId ||
        JSON.stringify(faqId) ===
          JSON.stringify({
            isTrusted: true,
          }))
    ) {
      // Start streaming timer when initiating streaming request
      startStreamingTimer();
      fetchResponseFromEventStream(props.chatflowid, body);
    } else {
      try {
        const result = await sendMessageQuery({
          chatflowid: props.chatflowid,
          apiHost: props.apiHost,
          body,
          onRequest: props.onRequest,
        });

        if (result.data) {
          const data = result.data;

          let text = '';
          if (data.text) text = data.text;
          else if (data.json) text = JSON.stringify(data.json, null, 2);
          else text = JSON.stringify(data, null, 2);

          if (data?.chatId) setChatId(data.chatId);

          playReceiveSound();

          setMessages((prevMessages) => {
            const allMessages = [...cloneDeep(prevMessages)];
            const newMessage = {
              message: text,
              id: data?.chatMessageId,
              sourceDocuments: data?.sourceDocuments,
              usedTools: data?.usedTools,
              fileAnnotations: data?.fileAnnotations,
              agentReasoning: data?.agentReasoning,
              action: data?.action,
              artifacts: data?.artifacts,
              type: 'apiMessage' as messageType,
              feedback: null,
              dateTime: new Date().toISOString(),
            };
            allMessages.push(newMessage);
            addChatMessage(allMessages);
            return allMessages;
          });

          updateMetadata(data, value);

          setLoading(false);
          setDisableInput(false);
          setUserInput('');
          setUploadedFiles([]);
          scrollToBottom();
        }
      } catch (error) {
        if (!navigator.onLine) {
          setIsNetworkError(true);
          setNetworkErrorMessage('Mất kết nối mạng');
          setMessages((prevMessages) => {
            const allMessages = [...cloneDeep(prevMessages)];
            allMessages.push({
              message: 'Xin lỗi, hiện tại không có kết nối mạng. Vui lòng kiểm tra lại kết nối và thử lại sau.',
              type: 'apiMessage',
              dateTime: new Date().toISOString(),
            });
            return allMessages;
          });
          setLoading(false);
          setDisableInput(false);
          setUserInput('');
          setUploadedFiles([]);
        } else {
          console.error(error);
          if (typeof error === 'object' && error !== null && 'message' in error) {
            handleError(`Error: ${(error as { message: string }).message.replaceAll('Error:', ' ')}`);
            return;
          }
          if (typeof error === 'string') {
            handleError(error);
            return;
          }
          handleError();
        }
        setLoading(false);
        setDisableInput(false);
        return;
      }
    }
  };

  const handleActionClick = async (label: string, action: IAction | undefined | null) => {
    setUserInput(label);
    setMessages((data) => {
      const updated = data.map((item, i) => {
        if (i === data.length - 1) {
          return { ...item, action: null };
        }
        return item;
      });
      addChatMessage(updated);
      return [...updated];
    });
    handleSubmit(label, action);
  };

  const clearChat = () => {
    try {
      removeLocalStorageChatHistory(props.chatflowid);
      setChatId(
        (props.chatflowConfig?.vars as any)?.customerId ? `${(props.chatflowConfig?.vars as any).customerId.toString()}+${uuidv4()}` : uuidv4(),
      );
      setUploadedFiles([]);
      const messages: MessageType[] = [
        {
          message: getWelcomeMessage(props),
          type: 'apiMessage',
        },
        ...(props.secondMessage ? [{ message: props.secondMessage, type: 'apiMessage' as messageType }] : []),
      ];
      if (leadsConfig()?.status && !getLocalStorageChatflow(props.chatflowid)?.lead) {
        messages.push({ message: '', type: 'leadCaptureMessage' });
      }
      setMessages(messages);
    } catch (error: any) {
      const errorData = error.response.data || `${error.response.status}: ${error.response.statusText}`;
      console.error(`error: ${errorData}`);
    }
  };

  (window as any).clearChat = clearChat;

  onMount(() => {
    let pending = false;
    const id = setInterval(() => {
      if (pending || document.hidden) return;
      const conversationId = localStorage.getItem(`chatwoot:${props.chatflowid}:conversation_id`);

      if (conversationId) {
        pending = true;
        sendMessageQuery({
          chatflowid: props.chatflowid,
          apiHost: props.apiHost,
          body: {
            question: 'ping',
            fetch: true,
            chatId: chatId(),
            stored: {
              chatwoot: {
                conversation_id: conversationId,
              },
              userInfo: (() => {
                const userInfoStr = localStorage.getItem('cmcts:userInfo');
                if (!userInfoStr) return undefined;
                try {
                  return JSON.parse(userInfoStr) as UserInfo;
                } catch (e) {
                  console.warn('Failed to parse userInfo from localStorage');
                  return undefined;
                }
              })(),
            },
          },
          onRequest: props.onRequest,
        })
          .then((result) => {
            if (!localStorage.getItem(`chatwoot:${props.chatflowid}:conversation_id`)) return;

            const storeKey = `chatwoot:${props.chatflowid}:conversation_id:latest_message_id`;
            const lastSyncMessageId = localStorage.getItem(storeKey);
            const enableSync = Boolean(lastSyncMessageId);
            const newMessages: any[] = result.data?.messages?.payload || [];
            const syncFromIndex = newMessages.findIndex((message) => message.id.toString() === lastSyncMessageId) + 1;
            let addMessages: MessageType[] = [];

            for (let i = syncFromIndex; i < newMessages.length; i++) {
              if (newMessages[i].message_type !== 1) continue;
              addMessages.push({
                message: newMessages[i].content,
                messageId: v4(),
                rating: undefined,
                sourceDocuments: [],
                type: 'apiMessage',
              });
            }

            if (addMessages.length) {
              if (!enableSync) {
                localStorage.setItem(storeKey, newMessages[newMessages.length - 1].id.toString());
                addMessages = [
                  {
                    message: 'Chào bạn, tôi là IT Operator – rất hân hạnh được hỗ trợ bạn. Bạn cần tôi giúp gì hôm nay?',
                    messageId: v4(),
                    rating: undefined,
                    sourceDocuments: [],
                    type: 'apiMessage',
                  },
                ];
              }

              setMessages((prevMessages) => {
                localStorage.setItem(storeKey, newMessages[newMessages.length - 1].id.toString());
                prevMessages.push(...addMessages);
                addChatMessage(prevMessages);

                return [...prevMessages].filter((message) => {
                  if (!enableSync) {
                    return !message.message?.includes('assistant_phone_number');
                  }

                  return true;
                });
              });
            }
          })
          .finally(() => {
            pending = false;
          });
      }
    }, 1000);

    return () => {
      clearInterval(id);
    };
  });

  onMount(() => {
    if (props.clearChatOnReload) {
      clearChat();
      window.addEventListener('beforeunload', clearChat);
      return () => {
        window.removeEventListener('beforeunload', clearChat);
      };
    }
  });

  onMount(() => {
    // Add network status event listeners
    const handleOnline = () => {
      setIsNetworkError(false);
      setNetworkErrorMessage('');
    };

    const handleOffline = () => {
      setIsNetworkError(true);
      setNetworkErrorMessage('Mất kết nối mạng');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  });

  onMount(() => {
    if (!chatContainer) return;

    chatContainer.addEventListener('scroll', () => {
      if (!chatContainer) return;
      if (chatContainer.scrollTop < chatContainer.scrollHeight - chatContainer.clientHeight - 50) {
        setShowScrollButton(true);
      } else {
        setShowScrollButton(false);
      }
    });
  });

  createEffect(() => {
    if (props.starterPrompts) {
      let prompts: string[];

      if (Array.isArray(props.starterPrompts)) {
        // If starterPrompts is an array
        prompts = props.starterPrompts;
      } else {
        // If starterPrompts is a JSON object
        prompts = Object.values(props.starterPrompts).map((promptObj: { prompt: string }) => promptObj.prompt);
      }

      // Filter out any empty prompts
      return setStarterPrompts(prompts.filter((prompt) => prompt !== ''));
    }
  });

  // Auto scroll chat to bottom
  createEffect(() => {
    if (messages()) {
      if (messages().length > 1) {
        setTimeout(() => {
          chatContainer?.scrollTo(0, chatContainer.scrollHeight);
        }, 400);
      }
    }
  });

  createEffect(() => {
    if (props.fontSize && botContainer) botContainer.style.fontSize = `${props.fontSize}px`;
  });

  const setSummaryMessage = (path: string, msg: string) => {
    setMessages((prevMessages) => {
      const allMessages = [...cloneDeep(prevMessages)];
      if (allMessages.find((message) => message.message.includes(msg))) {
        return allMessages;
      }

      const firstUserMessageIndex = allMessages.findIndex((message) => message.type === 'userMessage');
      const newMessage = {
        message: msg.trim() ? msg.trim() : `Không tìm thấy nội dung tóm tắt của file`,
        type: 'apiMessage' as messageType,
        dateTime: new Date().toISOString(),
      };
      if (firstUserMessageIndex !== -1) {
        allMessages.splice(firstUserMessageIndex, 0, newMessage);
        addChatMessage(allMessages);
      } else {
        allMessages.push(newMessage);
        addChatMessage(allMessages);
      }

      return allMessages;
    });
  };

  const getSummaryFile = async (path: string) => {
    if (!path) return;

    if (fileSummary()) {
      setSummaryMessage(path, fileSummary());
    } else {
      const response = await getFileContent({
        path: path,
        onRequest: props.onRequest,
      });

      if (response?.data?.content) {
        setFileSummary(response?.data?.content);
        setSummaryMessage(path, response?.data?.content);
      }
    }
  };

  const getContentFile = async (path: string) => {
    if (!path) return;

    if (!fileContent()) {
      const response = await getFileContent({
        path: path,
        onRequest: props.onRequest,
      });

      if (response?.data?.content) {
        setFileContent(response?.data?.content);
      }
    }
  };

  const getQuestionFile = async (path: string) => {
    if (!path) return;

    if (suggestQuestion().length === 0) {
      const response = await getFileContent({
        path: path,
        onRequest: props.onRequest,
      });

      if (response?.data?.content) {
        let suggestArr = [];
        try {
          suggestArr = JSON.parse(response?.data?.content);
        } catch (error) {
          console.log('🚀 ~ Bot.tsx:1266 ~ getQuestionFile ~ error:', error);
        }
        setSuggestQuestion(suggestArr);
      }
    }
  };

  createEffect(async () => {
    if (!props.filePath) {
      return;
    }
    let path = props.filePath;
    if (!path.startsWith('/')) path = '/' + path;
    if (!path.endsWith('.txt')) path += '.txt';
    const summaryPath = 'C_Office/summary' + path;
    const contentPath = 'C_Office/content' + path;
    const questionPath = 'C_Office/question' + path;

    // Lấy content và summary file khi chat có {props.filePath}
    if (messages().length === 1) {
      try {
        await Promise.all([getContentFile(contentPath), getSummaryFile(summaryPath), getQuestionFile(questionPath)]);
      } catch (error) {
        console.warn(`Error when reading ${props.filePath}`);
      }
    }
  });

  // eslint-disable-next-line solid/reactivity
  createEffect(async () => {
    if (!props?.chatflowid || !props?.apiHost) {
      return;
    }
    if (props.disclaimer) {
      if (getCookie('chatbotDisclaimer') == 'true') {
        setDisclaimerPopupOpen(false);
      } else {
        setDisclaimerPopupOpen(true);
      }
    } else {
      setDisclaimerPopupOpen(false);
    }

    const chatMessage = getLocalStorageChatflow(props.chatflowid);
    if (chatMessage && Object.keys(chatMessage).length) {
      if (chatMessage.chatId) setChatId(chatMessage.chatId);
      const savedLead = chatMessage.lead;
      if (savedLead) {
        setIsLeadSaved(!!savedLead);
        setLeadEmail(savedLead.email);
      }
      const loadedMessages: MessageType[] =
        chatMessage?.chatHistory?.length > 0
          ? chatMessage.chatHistory?.map((message: MessageType) => {
              const chatHistory: MessageType = {
                messageId: message?.messageId,
                message: message.message,
                type: message.type,
                rating: message.rating,
                dateTime: message.dateTime,
              };
              if (message.sourceDocuments) chatHistory.sourceDocuments = message.sourceDocuments;
              if (message.fileAnnotations) chatHistory.fileAnnotations = message.fileAnnotations;
              if (message.fileUploads) chatHistory.fileUploads = message.fileUploads;
              if (message.agentReasoning) chatHistory.agentReasoning = message.agentReasoning;
              if (message.action) chatHistory.action = message.action;
              if (message.artifacts) chatHistory.artifacts = message.artifacts;
              if (message.followUpPrompts) chatHistory.followUpPrompts = message.followUpPrompts;
              return chatHistory;
            })
          : [
              { message: getWelcomeMessage(props), type: 'apiMessage' },
              ...(props.secondMessage ? [{ message: props.secondMessage, type: 'apiMessage' as messageType }] : []),
            ];

      const filteredMessages = loadedMessages.filter((message) => message.type !== 'leadCaptureMessage');
      setMessages([...filteredMessages]);
    } else {
      setMessages([
        {
          message: getWelcomeMessage(props),
          type: 'apiMessage',
        },
        ...(props.secondMessage ? [{ message: props.secondMessage, type: 'apiMessage' as messageType }] : []),
      ]);
    }

    // Determine if particular chatflow is available for streaming
    const { data } = await isStreamAvailableQuery({
      chatflowid: props.chatflowid,
      apiHost: props.apiHost,
      onRequest: props.onRequest,
    });

    if (data) {
      setIsChatFlowAvailableToStream(data?.isStreaming ?? false);
    }

    // Get the chatbotConfig
    const result = await getChatbotConfig({
      chatflowid: props.chatflowid,
      apiHost: props.apiHost,
      onRequest: props.onRequest,
    });

    if (result.data) {
      if (!result.data?.welcomeMessage) {
        result.data.welcomeMessage = defaultWelcomeMessage;
      }

      const chatbotConfig = result.data;

      // Cache the config for future use
      setCachedChatbotConfig(props.chatflowid, chatbotConfig);

      // Handle welcomeMessage from API response
      if (chatbotConfig.welcomeMessage && !props.welcomeMessage) {
        const currentWelcomeMessage = messages()[0]?.message;
        if (currentWelcomeMessage !== chatbotConfig.welcomeMessage) {
          setMessages((prevMessages) => {
            const updatedMessages = [...prevMessages];
            if (updatedMessages.length > 0 && updatedMessages[0].type === 'apiMessage') {
              updatedMessages[0] = {
                ...updatedMessages[0],
                message: chatbotConfig.welcomeMessage,
              };
            }
            return updatedMessages;
          });
        }
      }
      if ((!props.starterPrompts || props.starterPrompts?.length === 0) && chatbotConfig.starterPrompts) {
        const prompts: string[] = [];
        Object.getOwnPropertyNames(chatbotConfig.starterPrompts).forEach((key) => {
          prompts.push(chatbotConfig.starterPrompts[key].prompt);
        });
        setStarterPrompts(prompts.filter((prompt) => prompt !== ''));
      }
      if (chatbotConfig.chatFeedback) {
        const chatFeedbackStatus = chatbotConfig.chatFeedback.status;
        setChatFeedbackStatus(chatFeedbackStatus);
      }
      if (chatbotConfig.uploads) {
        setUploadsConfig(chatbotConfig.uploads);
      }
      if (chatbotConfig.leads) {
        setLeadsConfig(chatbotConfig.leads);
        if (chatbotConfig.leads?.status && !getLocalStorageChatflow(props.chatflowid)?.lead) {
          setMessages((prevMessages) => [...prevMessages, { message: '', type: 'leadCaptureMessage' }]);
        }
      }
      if (chatbotConfig.followUpPrompts) {
        setFollowUpPromptsStatus(chatbotConfig.followUpPrompts.status);
      }
      if (chatbotConfig.fullFileUpload) {
        setFullFileUpload(chatbotConfig.fullFileUpload.status);
      }
    }

    // eslint-disable-next-line solid/reactivity
    return () => {
      setUserInput('');
      setUploadedFiles([]);
      setLoading(false);
      setDisableInput(false);
      setMessages([
        {
          message: getWelcomeMessage(props),
          type: 'apiMessage',
        },
        ...(props.secondMessage ? [{ message: props.secondMessage, type: 'apiMessage' as messageType }] : []),
      ]);
    };
  });

  createEffect(() => {
    if (followUpPromptsStatus() && messages().length > 0) {
      const lastMessage = messages()[messages().length - 1];
      if (lastMessage.type === 'apiMessage' && lastMessage.followUpPrompts) {
        setFollowUpPrompts(JSON.parse(lastMessage.followUpPrompts));
      } else if (lastMessage.type === 'userMessage') {
        setFollowUpPrompts([]);
      }
    }
  });

  const addRecordingToPreviews = (blob: Blob) => {
    let mimeType = '';
    const pos = blob.type.indexOf(';');
    if (pos === -1) {
      mimeType = blob.type;
    } else {
      mimeType = blob.type.substring(0, pos);
    }

    // read blob and add to previews
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    reader.onloadend = () => {
      const base64data = reader.result as FilePreviewData;
      const upload: FilePreview = {
        data: base64data,
        preview: '../assets/wave-sound.jpg',
        type: 'audio',
        name: `audio_${Date.now()}.wav`,
        mime: mimeType,
      };
      setPreviews((prevPreviews) => [...prevPreviews, upload]);
    };
  };

  const isFileAllowedForUpload = (file: File) => {
    let acceptFile = true;
    if (uploadsConfig() && uploadsConfig()?.isImageUploadAllowed && uploadsConfig()?.imgUploadSizeAndTypes) {
      const fileType = file.type;
      const sizeInMB = file.size / 1024 / 1024;
      uploadsConfig()?.imgUploadSizeAndTypes.map((allowed) => {
        if (allowed.fileTypes.includes(fileType) && sizeInMB <= allowed.maxUploadSize) {
          acceptFile = true;
        }
      });
    }
    if (fullFileUpload()) {
      return true;
    }
    if (uploadsConfig() && uploadsConfig()?.isRAGFileUploadAllowed && uploadsConfig()?.fileUploadSizeAndTypes) {
      const fileExt = file.name.split('.').pop();
      if (fileExt) {
        uploadsConfig()?.fileUploadSizeAndTypes.map((allowed) => {
          if (allowed.fileTypes.length === 1 && allowed.fileTypes[0] === '*') {
            acceptFile = true;
          } else if (allowed.fileTypes.includes(`.${fileExt}`)) {
            acceptFile = true;
          }
        });
      }
    }
    if (!acceptFile) {
      alert(`Cannot upload file. Kindly check the allowed file types and maximum allowed size.`);
    }
    return acceptFile;
  };

  const handleFileChange = async (event: FileEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) {
      return;
    }
    const filesList = [];
    const uploadedFiles = [];
    for (const file of files) {
      if (isFileAllowedForUpload(file) === false) {
        return;
      }
      // Only add files
      if (
        !uploadsConfig()
          ?.imgUploadSizeAndTypes.map((allowed) => allowed.fileTypes)
          .join(',')
          .includes(file.type)
      ) {
        uploadedFiles.push({ file, type: fullFileUpload() ? 'file:full' : 'file:rag' });
      }
      const reader = new FileReader();
      const { name } = file;
      filesList.push(
        new Promise((resolve) => {
          reader.onload = (evt) => {
            if (!evt?.target?.result) {
              return;
            }
            const { result } = evt.target;
            resolve({
              data: result,
              preview: URL.createObjectURL(file),
              type: 'file',
              name: name,
              mime: file.type,
            });
          };
          reader.readAsDataURL(file);
        }),
      );
    }

    const newFiles = await Promise.all(filesList);
    setUploadedFiles(uploadedFiles);
    setPreviews((prevPreviews) => [...prevPreviews, ...(newFiles as FilePreview[])]);
  };

  const isFileUploadAllowed = () => {
    if (fullFileUpload()) {
      return true;
    } else if (uploadsConfig()?.isRAGFileUploadAllowed) {
      return true;
    }
    return false;
  };

  const handleDrag = (e: DragEvent) => {
    if (uploadsConfig()?.isImageUploadAllowed || isFileUploadAllowed()) {
      e.preventDefault();
      e.stopPropagation();
      if (e.type === 'dragenter' || e.type === 'dragover') {
        setIsDragActive(true);
      } else if (e.type === 'dragleave') {
        setIsDragActive(false);
      }
    }
  };

  const handleDrop = async (e: InputEvent | DragEvent) => {
    if (!uploadsConfig()?.isImageUploadAllowed && !isFileUploadAllowed) {
      return;
    }
    e.preventDefault();
    setIsDragActive(false);
    const files = [];
    const uploadedFiles = [];
    if (e.dataTransfer && e.dataTransfer.files.length > 0) {
      for (const file of e.dataTransfer.files) {
        if (file.size > 10 * 1024 * 1024) {
          alert(`File size exceeds the maximum limit of 10MB. File name: ${file.name}`);
          return;
        }
        if (isFileAllowedForUpload(file) === false) {
          return;
        }
        // Only add files
        if (
          !uploadsConfig()
            ?.imgUploadSizeAndTypes.map((allowed) => allowed.fileTypes)
            .join(',')
            .includes(file.type)
        ) {
          uploadedFiles.push({ file, type: fullFileUpload() ? 'file:full' : 'file:rag' });
        }
        const reader = new FileReader();
        const { name } = file;
        files.push(
          new Promise((resolve) => {
            reader.onload = (evt) => {
              if (!evt?.target?.result) {
                return;
              }
              const { result } = evt.target;
              let previewUrl;
              if (file.type.startsWith('audio/')) {
                previewUrl = '../assets/wave-sound.jpg';
              } else if (file.type.startsWith('image/')) {
                previewUrl = URL.createObjectURL(file);
              }
              resolve({
                data: result,
                preview: previewUrl,
                type: 'file',
                name: name,
                mime: file.type,
              });
            };
            reader.readAsDataURL(file);
          }),
        );
      }

      const newFiles = await Promise.all(files);
      setUploadedFiles(uploadedFiles);
      setPreviews((prevPreviews) => [...prevPreviews, ...(newFiles as FilePreview[])]);
    }

    if (e.dataTransfer && e.dataTransfer.items) {
      for (const item of e.dataTransfer.items) {
        if (item.kind === 'string' && item.type.match('^text/uri-list')) {
          item.getAsString((s: string) => {
            const upload: FilePreview = {
              data: s,
              preview: s,
              type: 'url',
              name: s.substring(s.lastIndexOf('/') + 1),
              mime: '',
            };
            setPreviews((prevPreviews) => [...prevPreviews, upload]);
          });
        } else if (item.kind === 'string' && item.type.match('^text/html')) {
          item.getAsString((s: string) => {
            if (s.indexOf('href') === -1) return;
            //extract href
            const start = s.substring(s.indexOf('href') + 6);
            const hrefStr = start.substring(0, start.indexOf('"'));

            const upload: FilePreview = {
              data: hrefStr,
              preview: hrefStr,
              type: 'url',
              name: hrefStr.substring(hrefStr.lastIndexOf('/') + 1),
              mime: '',
            };
            setPreviews((prevPreviews) => [...prevPreviews, upload]);
          });
        }
      }
    }
  };

  const handleDeletePreview = (itemToDelete: FilePreview) => {
    if (itemToDelete.type === 'file') {
      URL.revokeObjectURL(itemToDelete.preview); // Clean up for file
    }
    setPreviews(previews().filter((item) => item !== itemToDelete));
  };

  const onMicrophoneClicked = () => {
    setIsRecording(true);
    startAudioRecording(setIsRecording, setRecordingNotSupported, setElapsedTime);
  };

  const onRecordingCancelled = () => {
    if (!recordingNotSupported) cancelAudioRecording();
    setIsRecording(false);
    setRecordingNotSupported(false);
  };

  const onRecordingStopped = async () => {
    setIsLoadingRecording(true);
    stopAudioRecording(addRecordingToPreviews);
  };

  const getInputDisabled = (): boolean => {
    const messagesArray = messages();
    const disabled =
      disableInput() ||
      !props.chatflowid ||
      (leadsConfig()?.status && !isLeadSaved()) ||
      (messagesArray[messagesArray.length - 1].action && Object.keys(messagesArray[messagesArray.length - 1].action as any).length > 0);
    if (disabled) {
      return true;
    }
    return false;
  };

  createEffect(
    // listen for changes in previews
    on(previews, (uploads) => {
      // wait for audio recording to load and then send
      const containsAudio = uploads.filter((item) => item.type === 'audio').length > 0;
      if (uploads.length >= 1 && containsAudio) {
        setIsRecording(false);
        setRecordingNotSupported(false);
        promptClick('');
      }

      return () => {
        setPreviews([]);
      };
    }),
  );

  const previewDisplay = (item: FilePreview) => {
    if (item.mime.startsWith('image/')) {
      return (
        <button
          class="group w-12 h-12 flex items-center justify-center relative rounded-[10px] overflow-hidden transition-colors duration-200"
          onClick={() => handleDeletePreview(item)}
        >
          <img class="w-full h-full bg-cover" src={item.data as string} />
          <span class="absolute hidden group-hover:flex items-center justify-center z-10 w-full h-full top-0 left-0 bg-black/10 rounded-[10px] transition-colors duration-200">
            <TrashIcon />
          </span>
        </button>
      );
    } else if (item.mime.startsWith('audio/')) {
      return (
        <div
          class={`inline-flex basis-auto flex-grow-0 flex-shrink-0 justify-between items-center rounded-xl h-12 p-1 mr-1 bg-gray-500`}
          style={{
            width: `${chatContainer ? (botProps.isFullPage ? chatContainer?.offsetWidth / 4 : chatContainer?.offsetWidth / 2) : '200'}px`,
          }}
        >
          <audio class="block bg-cover bg-center w-full h-full rounded-none text-transparent" controls src={item.data as string} />
          <button class="w-7 h-7 flex items-center justify-center bg-transparent p-1" onClick={() => handleDeletePreview(item)}>
            <TrashIcon color="white" />
          </button>
        </div>
      );
    } else {
      return <FilePreview disabled={getInputDisabled()} item={item} onDelete={() => handleDeletePreview(item)} />;
    }
  };

  const isUseFAQs = localStorage?.getItem('isUseFAQs');
  const existingChatflowData = isUseFAQs ? JSON.parse(isUseFAQs || '{}') : {}; // Ensure safe parsing
  const isUseFAQForChatflow = existingChatflowData[props?.chatflowid] === true ? true : false;

  return (
    <>
      <div
        ref={botContainer}
        class={'relative flex w-full h-full text-base overflow-hidden bg-cover bg-center flex-col items-center chatbot-container ' + props.class}
        onDragEnter={handleDrag}
      >
        {isDragActive() && (
          <div
            class="absolute top-0 left-0 bottom-0 right-0 w-full h-full z-50"
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragEnd={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          />
        )}
        {isDragActive() && (uploadsConfig()?.isImageUploadAllowed || isFileUploadAllowed()) && (
          <div
            class="absolute top-0 left-0 bottom-0 right-0 flex flex-col items-center justify-center bg-black/60 backdrop-blur-sm text-white z-40 gap-2 border-2 border-dashed"
            style={{ 'border-color': props.bubbleBackgroundColor }}
          >
            <h2 class="text-xl font-semibold">Drop here to upload</h2>
            <For each={[...(uploadsConfig()?.imgUploadSizeAndTypes || []), ...(uploadsConfig()?.fileUploadSizeAndTypes || [])]}>
              {(allowed) => {
                return (
                  <>
                    <span>{allowed.fileTypes?.join(', ')}</span>
                    {allowed.maxUploadSize && <span>Max Allowed Size: {allowed.maxUploadSize} MB</span>}
                  </>
                );
              }}
            </For>
          </div>
        )}
        {props.showTitle ? (
          <div
            class="flex flex-row items-center w-full h-[50px] absolute top-0 left-0 z-10"
            style={{
              background: '#005FAB',
              color: props.bubbleTextColor,
              'border-top-left-radius': props.isFullPage ? '0px' : '6px',
              'border-top-right-radius': props.isFullPage ? '0px' : '6px',
            }}
          >
            <Show when={props.titleAvatarSrc}>
              <>
                <div style={{ width: '15px' }} />
                <Avatar initialAvatarSrc={props.titleAvatarSrc} />
              </>
            </Show>
            <Show when={props.title}>
              <span class="px-3 whitespace-pre-wrap font-semibold max-w-full">{props.title}</span>
            </Show>
            <div style={{ flex: 1 }} />
            <div class="flex flex-row items-center">
              <Show when={props.onToggleFullScreen && !props.isFullPage}>
                <FullScreenButton
                  sendButtonColor={props.bubbleTextColor}
                  type="button"
                  isDisabled={loading()}
                  class="my-2 ml-2"
                  on:click={props.onToggleFullScreen}
                >
                  <span style={{ 'font-family': 'Poppins, sans-serif' }}>Full Screen</span>
                </FullScreenButton>
              </Show>
              <DeleteButton
                sendButtonColor={props.bubbleTextColor}
                type="button"
                isDisabled={messages().length === 1 || loading()}
                class="my-2 ml-2"
                on:click={clearChat}
              >
                <span style={{ 'font-family': 'Poppins, sans-serif' }}>Clear</span>
              </DeleteButton>
            </div>
          </div>
        ) : null}

        <div class="flex flex-col w-full h-full justify-start z-0">
          <div
            ref={chatContainer}
            class="overflow-y-auto flex flex-col flex-grow min-w-full w-full px-3 mt-[50px] pt-[30px] relative chatbot-chat-view scroll-smooth scrollbar-thin scrollbar-thumb-gray-500 scrollbar-track-gray-200"
            onclick={handleClick}
          >
            <For each={[...messages()]}>
              {(message, index) => {
                return (
                  <>
                    {message.type === 'userMessage' && (
                      <GuestBubble
                        message={message}
                        apiHost={props.apiHost}
                        chatflowid={props.chatflowid}
                        chatId={chatId()}
                        backgroundColor={props.userMessage?.backgroundColor}
                        textColor={props.userMessage?.textColor}
                        showAvatar={props.userMessage?.showAvatar}
                        avatarSrc={props.userMessage?.avatarSrc}
                        fontSize={props.fontSize}
                        renderHTML={props.renderHTML}
                      />
                    )}
                    {message.type === 'apiMessage' && Boolean(message.message) && (
                      <BotBubble
                        question={[...messages()][index() - 1]?.message}
                        message={message}
                        setMessages={setMessages}
                        fileAnnotations={message.fileAnnotations}
                        chatflowid={props.chatflowid}
                        chatId={chatId()}
                        apiHost={props.apiHost}
                        backgroundColor={props.botMessage?.backgroundColor}
                        textColor={props.botMessage?.textColor}
                        feedbackColor={props.feedback?.color}
                        showAvatar={props.botMessage?.showAvatar}
                        avatarSrc={props.botMessage?.avatarSrc}
                        chatFeedbackStatus={chatFeedbackStatus()}
                        fontSize={props.fontSize}
                        isLoading={loading() && index() === messages().length - 1}
                        showAgentMessages={props.showAgentMessages}
                        handleActionClick={(label, action) => handleActionClick(label, action)}
                        sourceDocsTitle={props.sourceDocsTitle}
                        handleSourceDocumentsClick={(sourceDocuments) => {
                          setSourcePopupSrc(sourceDocuments);
                          setSourcePopupOpen(true);
                        }}
                        dateTimeToggle={props.dateTimeToggle}
                        renderHTML={props.renderHTML}
                        chatwootUrl={props.chatwootUrl}
                        roomIds={props.roomIds}
                        onConnectToHumanAdvisor={(id, params) => {
                          axios
                            .post(`${props.apiHost}/api/v1/chatwoot/connect`, {
                              id,
                              chatId: props.chatflowid,
                              userInfo: (() => {
                                const userInfoStr = localStorage.getItem('cmcts:userInfo');
                                if (!userInfoStr) return undefined;
                                try {
                                  return JSON.parse(userInfoStr) as UserInfo;
                                } catch (e) {
                                  console.warn('Failed to parse userInfo from localStorage');
                                  return undefined;
                                }
                              })(),
                              params,
                              messages: localStorage.getItem(`${props.chatflowid}_EXTERNAL`) || '{"chatHistory":[]}',
                            })
                            .then(({ data }) => {
                              if (data?.conversation?.id) {
                                localStorage.setItem(`chatwoot:${props.chatflowid}:conversation_id`, data.conversation.id.toString());
                              }
                            });
                        }}
                        callBackIcon={props.callBackIcon}
                        callBackFunction={props.callBackFunction}
                        checkShowCallbackBtn={Boolean(props.filePath && index() === 1)}
                      />
                    )}
                    {message.type === 'leadCaptureMessage' && leadsConfig()?.status && !getLocalStorageChatflow(props.chatflowid)?.lead && (
                      <LeadCaptureBubble
                        message={message}
                        chatflowid={props.chatflowid}
                        chatId={chatId()}
                        apiHost={props.apiHost}
                        backgroundColor={props.botMessage?.backgroundColor}
                        textColor={props.botMessage?.textColor}
                        fontSize={props.fontSize}
                        showAvatar={props.botMessage?.showAvatar}
                        avatarSrc={props.botMessage?.avatarSrc}
                        leadsConfig={leadsConfig()}
                        sendButtonColor={props.textInput?.sendButtonColor}
                        isLeadSaved={isLeadSaved()}
                        setIsLeadSaved={setIsLeadSaved}
                        setLeadEmail={setLeadEmail}
                      />
                    )}
                    {message.type === 'userMessage' && loading() && index() === messages().length - 1 && (
                      <LoadingBubble streamingStartTime={streamingStartTime()} elapsedStreamingTime={elapsedStreamingTime()} />
                    )}
                    {message.type === 'apiMessage' && message.message === '' && loading() && index() === messages().length - 1 && (
                      <LoadingBubble streamingStartTime={streamingStartTime()} elapsedStreamingTime={elapsedStreamingTime()} />
                    )}
                  </>
                );
              }}
            </For>
          </div>
          <Show when={messages().length === 1}>
            <Show when={starterPrompts().length > 0}>
              <div class="w-full flex flex-row flex-wrap px-5 py-[10px] gap-2">
                <For each={[...starterPrompts()]}>
                  {(key) => (
                    <StarterPromptBubble
                      prompt={key}
                      onPromptClick={() => promptClick(key)}
                      starterPromptFontSize={botProps.starterPromptFontSize} // Pass it here as a number
                    />
                  )}
                </For>
              </div>
            </Show>
          </Show>
          <Show when={messages().length > 2 && followUpPromptsStatus()}>
            <Show when={followUpPrompts().length > 0}>
              <>
                <div class="flex items-center gap-1 px-5">
                  <SparklesIcon class="w-4 h-4" />
                  <span class="text-sm text-gray-700">Try these prompts</span>
                </div>
                <div class="w-full flex flex-row flex-wrap px-5 py-[10px] gap-2">
                  <For each={[...followUpPrompts()]}>
                    {(prompt, index) => (
                      <FollowUpPromptBubble
                        prompt={prompt}
                        onPromptClick={() => followUpPromptClick(prompt)}
                        starterPromptFontSize={botProps.starterPromptFontSize} // Pass it here as a number
                      />
                    )}
                  </For>
                </div>
              </>
            </Show>
          </Show>
          <PreviewImage previewImg={previewImage} setPreviewImg={setPreivewImage} />
          <Show when={previews().length > 0}>
            <div class="w-full flex items-center justify-start gap-2 px-5 pt-2 border-t border-[#eeeeee]">
              <For each={[...previews()]}>{(item) => <>{previewDisplay(item)}</>}</For>
            </div>
          </Show>
          {props.filePath && suggestQuestion().length > 0 && messages().length === 2 && !props.suggestQuestions && (
            <div class="flex flex-wrap gap-2 mt-2 ml-[24px]">
              <For each={suggestQuestion()}>
                {(suggestion) => (
                  <button
                    class="bg-gray-100 hover:bg-gray-200 text-sm text-gray-800 py-2 px-4 rounded-full transition-colors duration-200"
                    onClick={() => handleSugestSubmit(suggestion)}
                    disabled={loading()}
                  >
                    {suggestion}
                  </button>
                )}
              </For>
            </div>
          )}
          {props.suggestQuestions && (
            <div class="flex flex-wrap gap-2 mt-2 ml-[24px]">
              <For
                each={((): any => {
                  try {
                    return typeof props.suggestQuestions === 'string'
                      ? (JSON.parse(props.suggestQuestions) as string[]).filter(
                          (q) =>
                            !messages()
                              .filter((msg) => msg.type === 'userMessage')
                              .map((msg) => msg.message)
                              .includes(q),
                        )
                      : [];
                  } catch (error) {
                    console.error('Failed to parse suggestQuestions:', error);
                    return [];
                  }
                })()}
              >
                {(suggestion) => (
                  <button
                    class="bg-gray-100 hover:bg-gray-200 text-sm text-gray-800 py-2 px-4 rounded-full transition-colors duration-200"
                    onClick={() => handleSugestSubmit(suggestion)}
                    disabled={loading()}
                  >
                    {suggestion}
                  </button>
                )}
              </For>
            </div>
          )}
          <div class="w-full px-5 pt-2 pb-1">
            {isRecording() ? (
              <>
                {recordingNotSupported() ? (
                  <div class="w-full flex items-center justify-between p-4 border border-[#eeeeee]">
                    <div class="w-full flex items-center justify-between gap-3">
                      <span class="text-base">To record audio, use modern browsers like Chrome or Firefox that support audio recording.</span>
                      <button
                        class="py-2 px-4 justify-center flex items-center bg-red-500 text-white rounded-md"
                        type="button"
                        onClick={() => onRecordingCancelled()}
                      >
                        Okay
                      </button>
                    </div>
                  </div>
                ) : (
                  <div
                    class="h-[58px] flex items-center justify-between chatbot-input border border-[#eeeeee]"
                    data-testid="input"
                    style={{
                      margin: 'auto',
                      'background-color': props.textInput?.backgroundColor ?? defaultBackgroundColor,
                      color: props.textInput?.textColor ?? defaultTextColor,
                    }}
                  >
                    <div class="flex items-center gap-3 px-4 py-2">
                      <span>
                        <CircleDotIcon color="red" />
                      </span>
                      <span>{elapsedTime() || '00:00'}</span>
                      {isLoadingRecording() && <span class="ml-1.5">Sending...</span>}
                    </div>
                    <div class="flex items-center">
                      <CancelButton buttonColor={props.textInput?.sendButtonColor} type="button" class="m-0" on:click={onRecordingCancelled}>
                        <span style={{ 'font-family': 'Poppins, sans-serif' }}>Send</span>
                      </CancelButton>
                      <SendButton
                        sendButtonColor={props.textInput?.sendButtonColor}
                        type="button"
                        isDisabled={loading()}
                        class="m-0"
                        on:click={onRecordingStopped}
                      >
                        <span style={{ 'font-family': 'Poppins, sans-serif' }}>Send</span>
                      </SendButton>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <>
                {currentStatusMsg() && loading() && (
                  <div
                    style={{
                      color: props.textInput?.textColor ?? '#666666',
                      'font-size': '12px',
                      opacity: '0.7',
                      'font-style': 'italic',
                      'margin-bottom': '4px',
                      // 'text-align': 'center',
                    }}
                  >
                    {currentStatusMsg()}
                  </div>
                )}
                <TextInput
                  backgroundColor={props.textInput?.backgroundColor}
                  textColor={props.textInput?.textColor}
                  placeholder={props.textInput?.placeholder}
                  sendButtonColor={props.textInput?.sendButtonColor}
                  maxChars={props.textInput?.maxChars}
                  maxCharsWarningMessage={props.textInput?.maxCharsWarningMessage}
                  autoFocus={props.textInput?.autoFocus}
                  fontSize={props.fontSize}
                  disabled={getInputDisabled()}
                  inputValue={userInput()}
                  onInputChange={(value) => setUserInput(value)}
                  onSubmit={handleSubmit}
                  uploadsConfig={uploadsConfig()}
                  isFullFileUpload={fullFileUpload()}
                  setPreviews={setPreviews}
                  onMicrophoneClicked={onMicrophoneClicked}
                  handleFileChange={handleFileChange}
                  sendMessageSound={props.textInput?.sendMessageSound}
                  sendSoundLocation={props.textInput?.sendSoundLocation}
                  enableInputHistory={true}
                  maxHistorySize={10}
                  isLoading={loading()}
                  chatflowid={props.chatflowid}
                  apiHost={props.apiHost}
                  isUseFAQ={Boolean(props?.isUseFAQ) || isUseFAQForChatflow}
                  setDisableInput={setDisableInput}
                />
              </>
            )}
          </div>
          <Badge
            footer={props.footer}
            badgeBackgroundColor={props.badgeBackgroundColor}
            poweredByTextColor={props.poweredByTextColor}
            botContainer={botContainer}
          />
        </div>
        <Show when={showScrollButton()}>
          <button
            onClick={() => chatContainer?.scrollTo({ top: chatContainer.scrollHeight, behavior: 'smooth' })}
            class="fixed bottom-28 left-1/2 transform -translate-x-1/2 bg-white border border-gray-300 shadow-lg p-2 rounded-full flex items-center justify-center transition-opacity duration-300 hover:bg-gray-200 scale-90"
            aria-label="Scroll to bottom"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="black"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M12 5v14M19 12l-7 7-7-7" />
            </svg>
          </button>
        </Show>
      </div>
      {sourcePopupOpen() && <Popup isOpen={sourcePopupOpen()} value={sourcePopupSrc()} onClose={() => setSourcePopupOpen(false)} />}

      {disclaimerPopupOpen() && (
        <DisclaimerPopup
          isOpen={disclaimerPopupOpen()}
          onAccept={handleDisclaimerAccept}
          title={props.disclaimer?.title}
          message={props.disclaimer?.message}
          textColor={props.disclaimer?.textColor}
          buttonColor={props.disclaimer?.buttonColor}
          buttonText={props.disclaimer?.buttonText}
          buttonTextColor={props.disclaimer?.buttonTextColor}
          blurredBackgroundColor={props.disclaimer?.blurredBackgroundColor}
          backgroundColor={props.disclaimer?.backgroundColor}
        />
      )}

      <Show when={isNetworkError()}>
        <Toast
          message={networkErrorMessage()}
          type="error"
          duration={5000}
          onClose={() => {
            setIsNetworkError(false);
            setNetworkErrorMessage('');
          }}
        />
      </Show>
    </>
  );
};
