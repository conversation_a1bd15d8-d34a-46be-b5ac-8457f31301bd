import { createEffect, createSignal, For, onMount, Show } from 'solid-js';
import { Avatar } from '../avatars/Avatar';
import { Marked } from '@ts-stack/markdown';
import { FeedbackRatingType, getFeedbackByMessageId, sendFeedbackQuery, sendFileDownloadQuery } from '@/queries/sendMessageQuery';
import { FileUpload, IAction, MessageType } from '../Bot';
import { ThumbsDownButton, ThumbsUpButton } from '../buttons/FeedbackButtons';
import FeedbackContentDialog from '../FeedbackContentDialog';
import { AgentReasoningBubble } from './AgentReasoningBubble';
import { TickIcon, XIcon } from '../icons';
import { SourceBubble } from '../bubbles/SourceBubble';
import { DateTimeToggleTheme } from '@/features/bubble/types';
import { parseJsonMarkdown } from '@/utils/ultimateJsonParser';

type Props = {
  message: MessageType;
  setMessages?: any;
  chatflowid: string;
  chatId: string;
  apiHost?: string;
  onRequest?: (request: RequestInit) => Promise<void>;
  fileAnnotations?: any;
  showAvatar?: boolean;
  avatarSrc?: string;
  backgroundColor?: string;
  textColor?: string;
  chatFeedbackStatus?: boolean;
  fontSize?: number;
  feedbackColor?: string;
  isLoading: boolean;
  dateTimeToggle?: DateTimeToggleTheme;
  showAgentMessages?: boolean;
  sourceDocsTitle?: string;
  renderHTML?: boolean;
  handleActionClick: (label: string, action: IAction | undefined | null) => void;
  handleSourceDocumentsClick: (src: any) => void;
  chatwootUrl?: string;
  onConnectToHumanAdvisor?: (id: string, params?: any) => void;
  roomIds?: Record<string, string>;
  question?: string;
  callBackIcon?: string;
  callBackFunction?: (e: any) => void;
  checkShowCallbackBtn?: boolean;
};

const defaultBackgroundColor = '#f7f8ff';
const defaultTextColor = '#303235';
const defaultFontSize = 16;
const defaultFeedbackColor = '#006400';

const convertNewlinesToBr = (text: string) => {
  return text.replace(/\n/g, '<br>');
};

export const BotBubble = (props: Props) => {
  // console.log('🚀 ~ BotBubble.tsx:55 ~ BotBubble ~ props:', props);
  let botMessageEl: HTMLDivElement | undefined;
  let botDetailsEl: HTMLDetailsElement | undefined;
  const [isConnecting, setIsConnecting] = createSignal(false);
  const [inputValue, setInputValue] = createSignal('');

  const convertEmailToMarkdownLink = (text: string) => {
    return text.replace(/<([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,})>/g, (match, email) => {
      return `[${email}](mailto:${email})`;
    });
  };

  Marked.setOptions({
    isNoP: true,
    sanitize: props.renderHTML !== undefined ? !props.renderHTML : true,
    breaks: true
  });

  const [rating, setRating] = createSignal('');
  const [tempRating, setTempRating] = createSignal<FeedbackRatingType | ''>('');
  const [showFeedbackContentDialog, setShowFeedbackContentModal] = createSignal(false);

  const downloadFile = async (fileAnnotation: any) => {
    try {
      const response = await sendFileDownloadQuery({
        apiHost: props.apiHost,
        body: { fileName: fileAnnotation.fileName, chatflowId: props.chatflowid, chatId: props.chatId } as any,
        onRequest: props.onRequest,
      });
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileAnnotation.fileName;
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const saveToLocalStorage = (rating: FeedbackRatingType) => {
    const chatDetails = localStorage.getItem(`${props.chatflowid}_EXTERNAL`);
    if (!chatDetails) return;
    try {
      const parsedDetails = JSON.parse(chatDetails);
      const messages: MessageType[] = parsedDetails.chatHistory || [];
      const message = messages.find((msg) => msg.messageId === props.message.messageId);
      if (!message) return;
      message.rating = rating;
      localStorage.setItem(`${props.chatflowid}_EXTERNAL`, JSON.stringify({ ...parsedDetails, chatHistory: messages }));
    } catch (e) {
      return;
    }
  };

  const isValidURL = (url: string): URL | undefined => {
    try {
      if (!url.startsWith('http')) return undefined;
      return new URL(url);
    } catch (err) {
      return undefined;
    }
  };

  const removeDuplicateURL = (message: MessageType) => {
    const visitedURLs: string[] = [];
    const newSourceDocuments: any = [];

    message.sourceDocuments.forEach((source: any) => {
      if (isValidURL(source.metadata.source) && !visitedURLs.includes(source.metadata.source)) {
        visitedURLs.push(source.metadata.source);
        newSourceDocuments.push(source);
      } else if (!isValidURL(source.metadata.source)) {
        newSourceDocuments.push(source);
      }
    });

    return newSourceDocuments;
  };

  const onThumbsUpClick = async () => {
    setTempRating('THUMBS_UP');

    const result = await getFeedbackByMessageId({
      messageId: props.message?.messageId as string,
      apiHost: props.apiHost,
    });

    const data = result?.data as any;

    if (data) {
      setInputValue(data?.content);
    }
    setShowFeedbackContentModal(true);

    if (rating() !== '') {
      return;
    }
  };

  const onThumbsDownClick = async () => {
    setTempRating('THUMBS_DOWN');

    const result = await getFeedbackByMessageId({
      messageId: props.message?.messageId as string,
      apiHost: props.apiHost,
    });

    const data = result?.data as any;

    if (data) {
      setInputValue(data?.content);
    }
    setShowFeedbackContentModal(true);
    if (rating() !== '') {
      return;
    }
  };

  const submitFeedbackContent = async (text: string) => {
    const trimmedText = text.trim();

    if (!trimmedText) return;

    const bodyReact = {
      chatflowid: props.chatflowid,
      chatId: props.chatId,
      messageId: props.message?.messageId as string,
      rating: tempRating() as FeedbackRatingType,
      // content: '',
      content: trimmedText,
      question: props.question || '',
      answer: props.message.message || '',
    };

    const result = await sendFeedbackQuery({
      chatflowid: props.chatflowid,
      apiHost: props.apiHost,
      body: bodyReact,
      onRequest: props.onRequest,
    });

    const data = result.data as any;

    if (data) {
      // const bodyContent = {
      //   content: trimmedText,
      //   rating: tempRating(),
      // };

      // const resultContent = await updateFeedbackQuery({
      //   id: data?.id || '',
      //   apiHost: props.apiHost,
      //   body: bodyContent,
      //   onRequest: props.onRequest,
      // });

      // if (resultContent.data) {
      setRating(tempRating());
      setShowFeedbackContentModal(false);
      saveToLocalStorage(tempRating() as FeedbackRatingType);
      setInputValue('');
      if (typeof props.setMessages === 'function' && tempRating()) {
        props.setMessages((prevMessages: MessageType[]) =>
          prevMessages.map((message: MessageType) =>
            message.messageId === props?.message?.messageId ? { ...message, rating: tempRating() } : message,
          ),
        );
      }
      // }
    }
  };

  onMount(() => {
    if (botMessageEl) {
      const safeMessage = convertEmailToMarkdownLink(props.message.message);
      botMessageEl.innerHTML = Marked.parse(safeMessage);
      botMessageEl.querySelectorAll('a').forEach((link) => {
        link.target = '_blank';
      });
      if (props.message.rating) {
        setRating(props.message.rating);
      }
      if (props.fileAnnotations && props.fileAnnotations.length) {
        for (const annotations of props.fileAnnotations) {
          const button = document.createElement('button');
          button.textContent = annotations.fileName;
          button.className =
            'py-2 px-4 mb-2 justify-center font-semibold text-white focus:outline-none flex items-center disabled:opacity-50 disabled:cursor-not-allowed disabled:brightness-100 transition-all filter hover:brightness-90 active:brightness-75 file-annotation-button';
          button.addEventListener('click', function () {
            downloadFile(annotations);
          });
          const svgContainer = document.createElement('div');
          svgContainer.className = 'ml-2';
          svgContainer.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-download" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="#ffffff" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2" /><path d="M7 11l5 5l5 -5" /><path d="M12 4l0 12" /></svg>`;

          button.appendChild(svgContainer);
          botMessageEl.appendChild(button);
        }
      }
    }

    if (botDetailsEl && props.isLoading) {
      botDetailsEl.open = true;
    }
  });

  createEffect(() => {
    if (botDetailsEl && props.isLoading) {
      botDetailsEl.open = true;
    } else if (botDetailsEl && !props.isLoading) {
      botDetailsEl.open = false;
    }
  });

  const renderArtifacts = (item: Partial<FileUpload>) => {
    return (
      <>
        <Show when={item.type === 'png' || item.type === 'jpeg'}>
          <div class="flex items-center justify-center p-0 m-0">
            <img
              class="w-full h-full bg-cover"
              src={((): any => {
                const isFileStorage = typeof item.data === 'string' && item.data.startsWith('FILE-STORAGE::');
                return isFileStorage
                  ? `${props.apiHost}/api/v1/get-upload-file?chatflowId=${props.chatflowid}&chatId=${props.chatId}&fileName=${(
                      item.data as string
                    ).replace('FILE-STORAGE::', '')}`
                  : (item.data as string);
              })()}
            />
          </div>
        </Show>
        <Show when={item.type === 'html'}>
          <div class="mt-2">
            <div innerHTML={item.data as string} />
          </div>
        </Show>
        <Show when={item.type !== 'png' && item.type !== 'jpeg' && item.type !== 'html'}>
          <span
            innerHTML={Marked.parse(item.data as string)}
            class="prose"
            style={{
              'background-color': props.backgroundColor ?? defaultBackgroundColor,
              color: props.textColor ?? defaultTextColor,
              'border-radius': '6px',
              'font-size': props.fontSize ? `${props.fontSize}px` : `${defaultFontSize}px`,
            }}
          />
        </Show>
      </>
    );
  };

  const formatDateTime = (dateTimeString: string | undefined, showDate: boolean | undefined, showTime: boolean | undefined) => {
    if (!dateTimeString) return '';

    try {
      const date = new Date(dateTimeString);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.error('Invalid ISO date string:', dateTimeString);
        return '';
      }

      let formatted = '';

      if (showDate) {
        const dateFormatter = new Intl.DateTimeFormat('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        });
        const [{ value: month }, , { value: day }, , { value: year }] = dateFormatter.formatToParts(date);
        formatted = `${month.charAt(0).toUpperCase() + month.slice(1)} ${day}, ${year}`;
      }

      if (showTime) {
        const timeFormatter = new Intl.DateTimeFormat('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        });
        const timeString = timeFormatter.format(date).toLowerCase();
        formatted = formatted ? `${formatted}, ${timeString}` : timeString;
      }

      return formatted;
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  let assistant: any = null;

  // props.message?.agentReasoning?.forEach((reason: any) => {
  //   if (reason?.agentName !== 'human_router') return false;
  //   const message = reason.messages.find((msg: any) => msg.includes('assistant_phone_number'));
  //   if (!message) return false;
  //
  //   try {
  //     const messageJson = JSON.parse(message);
  //     const { assistant_name, assistant_url, assistant_phone_number, assistant_params, assistant_id } = messageJson;
  //
  //     console.log('props.roomIds:', props.roomIds);
  //
  //     const ids: Record<string, string> = {
  //       '012345678': '4KxYsWCYCmz63wxQhxFZytYD', // CMC UNI thủ tục nhập học
  //       '023456781': 'vfFLPcSw6TabMUJ96en3esQR', // CMC UNI Chuyên ngành học
  //       '034567812': 'QCu8z3S6kLF4t2mEEZi491Ei', // Học phí
  //       '045678123': 'vuZxeabSr5qf2Yug6aK9dRak', // CMC UNI công tác sinh viên
  //       '056781234': 'XJHsEH4BbiBimaLdWLN1Eccx', // CMC UNI General
  //       ...props.roomIds,
  //     };
  //
  //     const baseUrl = assistant_url || `${props.chatwootUrl}/widget?website_token=${ids[assistant_phone_number]}` || '#';
  //     const separator = baseUrl.includes('?') ? '&' : '?';
  //     const params = encodeURIComponent(JSON.stringify(assistant_params));
  //
  //     assistant = {
  //       label: assistant_name,
  //       url: `${baseUrl}${separator}params=${params}`,
  //       id: assistant_id || ids[assistant_phone_number],
  //     };
  //   } catch (error) {
  //     console.error('Error parsing assistant message:', error);
  //   }
  // });

  if (props.message.sourceDocuments && props.message.sourceDocuments.length === 0 && props.message.agentReasoning) {
    const allSourceDocs = props.message.agentReasoning.reduce((acc: any[], agent) => {
      if (agent.sourceDocuments && agent.sourceDocuments.length > 0) {
        acc.push(...agent.sourceDocuments.filter((doc: any) => doc !== null));
      }
      return acc;
    }, []);

    props.message.sourceDocuments = allSourceDocs;
  }

  if (props.message?.message?.includes('assistant_phone_number')) {
    try {
      const messageJson: {
        assistant_name?: string;
        assistant_url?: string;
        assistant_phone_number?: string;
        assistant_params?: any;
        assistant_id?: string;
      } = parseJsonMarkdown(props.message.message);
      const { assistant_name, assistant_url, assistant_params, assistant_id } = messageJson;
      const baseUrl = assistant_url || `${props.chatwootUrl}/widget?website_token=Xa6HmhfbU5K73ZhHnspzKhqs` || '#';
      const separator = baseUrl.includes('?') ? '&' : '?';
      const params = encodeURIComponent(JSON.stringify(assistant_params));

      assistant = {
        label: assistant_name || 'Admin',
        url: `${baseUrl}${separator}params=${params || '{}'}`,
        id: assistant_id || 'Xa6HmhfbU5K73ZhHnspzKhqs',
      };

      console.log('baseUrl', baseUrl, assistant);
    } catch (e) {
      console.warn(e);
    }

    // props.message.message = 'Đã gửi yêu cầu của bạn đến bộ phận hỗ trợ';
  }

  const handleCallback = (message: string) => {
    props.callBackFunction?.(message);
  };

  return (
    <div>
      {!assistant?.label && (
        <>
          <div class="flex flex-row justify-start mb-2 items-start host-container" style={{ 'margin-right': '50px' }}>
            <Show when={props.showAvatar}>
              <Avatar initialAvatarSrc={props.avatarSrc} />
            </Show>
            <div class="flex flex-col justify-start relative">
              {props.showAgentMessages && props.message.agentReasoning && (
                <details ref={botDetailsEl} class="mb-2 px-4 py-2 ml-2 chatbot-host-bubble rounded-[6px]">
                  <summary class="cursor-pointer">
                    <span class="italic">Agent Messages</span>
                  </summary>
                  <br />
                  <For each={props.message.agentReasoning}>
                    {(agent) => {
                      const agentMessages = agent.messages ?? [];
                      let msgContent = agent.instructions || (agentMessages.length > 1 ? agentMessages.join('\\n') : agentMessages[0]);
                      if (agentMessages.length === 0 && !agent.instructions) msgContent = `<p>Finished</p>`;
                      return (
                        <AgentReasoningBubble
                          agentName={agent.agentName ?? ''}
                          agentMessage={msgContent}
                          agentArtifacts={agent.artifacts}
                          backgroundColor={props.backgroundColor}
                          textColor={props.textColor}
                          fontSize={props.fontSize}
                          apiHost={props.apiHost}
                          chatflowid={props.chatflowid}
                          chatId={props.chatId}
                          renderHTML={props.renderHTML}
                        />
                      );
                    }}
                  </For>
                </details>
              )}
              {props.message.artifacts && props.message.artifacts.length > 0 && (
                <div class="flex flex-row items-start flex-wrap w-full gap-2">
                  <For each={props.message.artifacts}>
                    {(item) => {
                      return item !== null ? <>{renderArtifacts(item)}</> : null;
                    }}
                  </For>
                </div>
              )}
              {props.message.message && (
                <span
                  ref={botMessageEl}
                  class="px-4 py-2 ml-2 max-w-full chatbot-host-bubble prose"
                  data-testid="host-bubble"
                  style={{
                    'background-color': props.backgroundColor ?? defaultBackgroundColor,
                    color: props.textColor ?? defaultTextColor,
                    'border-radius': '6px',
                    'font-size': props.fontSize ? `${props.fontSize}px` : `${defaultFontSize}px`,
                  }}
                />
              )}
              {props.checkShowCallbackBtn && props.callBackIcon && (
                <div class="cursor-pointer" onClick={() => handleCallback(props.message.message)} innerHTML={props.callBackIcon} />
              )}

              {props.message.action && (
                <div class="px-4 py-2 flex flex-row justify-start space-x-2">
                  <For each={props.message.action.elements || []}>
                    {(action) => {
                      return (
                        <>
                          {action.type === 'approve-button' ? (
                            <button
                              type="button"
                              class="px-4 py-2 font-medium text-green-600 border border-green-600 rounded-full hover:bg-green-600 hover:text-white transition-colors duration-300 flex items-center space-x-2"
                              onClick={() => props.handleActionClick(action.label, props.message.action)}
                            >
                              <TickIcon />
                              &nbsp;
                              {action.label}
                            </button>
                          ) : action.type === 'reject-button' ? (
                            <button
                              type="button"
                              class="px-4 py-2 font-medium text-red-600 border border-red-600 rounded-full hover:bg-red-600 hover:text-white transition-colors duration-300 flex items-center space-x-2"
                              onClick={() => props.handleActionClick(action.label, props.message.action)}
                            >
                              <XIcon isCurrentColor={true} />
                              &nbsp;
                              {action.label}
                            </button>
                          ) : (
                            <button>{action.label}</button>
                          )}
                        </>
                      );
                    }}
                  </For>
                </div>
              )}
            </div>
          </div>
          <div>
            {props.message.sourceDocuments && props.message.sourceDocuments.length && (
              <>
                <Show when={props.sourceDocsTitle}>
                  <span class="px-2 py-[10px] font-semibold">{props.sourceDocsTitle}</span>
                </Show>
                <div style={{ display: 'flex', 'flex-direction': 'row', width: '100%', 'flex-wrap': 'wrap' }}>
                  <For each={[...removeDuplicateURL(props.message)]}>
                    {(src) => {
                      const URL = isValidURL(src.metadata.source);
                      return (
                        <SourceBubble
                          src={src}
                          pageContent={URL ? URL.pathname : src.pageContent}
                          metadata={src.metadata}
                          onSourceClick={() => {
                            if (URL) {
                              window.open(src.metadata.source, '_blank');
                            } else {
                              props.handleSourceDocumentsClick(src);
                            }
                          }}
                        />
                      );
                    }}
                  </For>
                </div>
              </>
            )}
          </div>
          <div>
            {props.chatFeedbackStatus && props.message.messageId && (
              <>
                <div class={`flex items-center px-2 pb-2 ${props.showAvatar ? 'ml-10' : ''}`}>
                  <ThumbsUpButton
                    feedbackColor={rating() === 'THUMBS_UP' ? '#00A5F0' : (props.feedbackColor ?? defaultFeedbackColor)}
                    rating={rating()}
                    onClick={onThumbsUpClick}
                  />
                  <ThumbsDownButton
                    feedbackColor={rating() === 'THUMBS_DOWN' ? '#EC808D' : (props.feedbackColor ?? defaultFeedbackColor)}
                    rating={rating()}
                    onClick={onThumbsDownClick}
                  />
                  <Show when={props.message.dateTime}>
                    <div class="text-sm text-gray-500 ml-2">
                      {formatDateTime(props.message.dateTime, props?.dateTimeToggle?.date, props?.dateTimeToggle?.time)}
                    </div>
                  </Show>
                </div>
                <Show when={showFeedbackContentDialog()}>
                  <FeedbackContentDialog
                    isOpen={showFeedbackContentDialog()}
                    onClose={() => setShowFeedbackContentModal(false)}
                    onSubmit={submitFeedbackContent}
                    backgroundColor={props.backgroundColor}
                    textColor={props.textColor}
                    inputValue={inputValue}
                    setInputValue={setInputValue}
                  />
                </Show>
              </>
            )}
          </div>
        </>
      )}
      {Boolean(assistant?.label) && (
        <div class="flex justify-center pb-5">
          <button
            disabled={isConnecting()}
            onClick={() => {
              setIsConnecting(true);
              props.onConnectToHumanAdvisor?.(assistant.id, assistant);
              setTimeout(() => {
                setIsConnecting(false);
              }, 1500);
            }}
            class="px-4 h-[45px] py-2 border border-[#3B81F6] rounded text-[#3B81F6] hover:bg-[#3B81F6] hover:text-white transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isConnecting() ? (
              <span class="inline-flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                Đang kết nối...
              </span>
            ) : (
              `Nhấn vào đây để kết nối đến ${assistant.label}`
            )}
          </button>
        </div>
      )}
    </div>
  );
};
