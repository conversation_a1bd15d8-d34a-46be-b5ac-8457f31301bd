import styles from '../../../assets/index.css';
import { createEffect, createSignal, onCleanup, onMount, Show, splitProps } from 'solid-js';
import { isNotDefined } from '@/utils/index';
import { Marked } from '@ts-stack/markdown';
import axios from 'axios';
import * as pdfjs from 'pdfjs-dist';

// Initialize PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

export type PopupProps = {
  value?: any;
  isOpen?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
};

function syntaxHighlight(json: any) {
  if (typeof json != 'string') {
    json = JSON.stringify(json, undefined, 2);
  }
  json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
  // eslint-disable-next-line
  return json.replace(
    /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+-]?\d+)?)/g,
    function (match: string) {
      let cls = 'number';
      if (/^"/.test(match)) {
        if (/:$/.test(match)) {
          cls = 'key';
        } else {
          cls = 'string';
        }
      } else if (/true|false/.test(match)) {
        cls = 'boolean';
      } else if (/null/.test(match)) {
        cls = 'null';
      }
      return '<span class="' + cls + '">' + match + '</span>';
    },
  );
}

export const Popup = (props: PopupProps) => {
  let preEl: HTMLDivElement | undefined;
  let canvasRefs: HTMLCanvasElement[] = [];

  // Configure Marked for markdown rendering
  Marked.setOptions({
    isNoP: true,
    sanitize: true,
  });

  const [isLoading, setIsLoading] = createSignal(false);
  const [pdfDoc, setPdfDoc] = createSignal<pdfjs.PDFDocumentProxy | null>(null);
  const [error, setError] = createSignal<string | null>(null);

  const [popupProps] = splitProps(props, ['onOpen', 'onClose', 'isOpen', 'value']);

  const renderAllPages = async () => {
    const pdf = pdfDoc();
    if (!pdf) return;

    try {
      const pages = [];
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        pages.push(page);
      }

      // Create canvas elements for each page
      const container = document.createElement('div');
      container.style.display = 'flex';
      container.style.flexDirection = 'column';
      container.style.gap = '20px';
      container.style.alignItems = 'center';
      container.style.maxHeight = '80vh';
      container.style.overflowY = 'auto';
      container.style.padding = '20px';

      // Clear any existing content
      if (preEl) {
        preEl.innerHTML = '';
        preEl.appendChild(container);
      }

      // Render each page
      for (let i = 0; i < pages.length; i++) {
        const page = pages[i];
        const canvas = document.createElement('canvas');
        canvasRefs.push(canvas);
        container.appendChild(canvas);

        const viewport = page.getViewport({ scale: 1.5 });
        const context = canvas.getContext('2d');

        if (!context) {
          setError('Failed to get canvas context');
          return;
        }

        canvas.height = viewport.height;
        canvas.width = viewport.width;
        canvas.style.maxWidth = '100%';
        canvas.style.height = 'auto';

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        await page.render(renderContext).promise;
      }
    } catch (err) {
      const error = err as Error;
      setError('Failed to render PDF pages: ' + error.message);
    }
  };

  createEffect(() => {
    if (pdfDoc()) {
      void renderAllPages();
    }
  });

  onMount(async () => {
    if (preEl) {
      if (props.value?.metadata?.source?.endsWith('.pdf')) {
        setIsLoading(true);
        setError(null);

        let downloadSource: any = props.value.metadata.source || '';
        if (downloadSource.startsWith('s3://')) {
          downloadSource = downloadSource.replace('s3://', '');
          downloadSource = downloadSource.split('/') as string[];
          downloadSource.shift();
          downloadSource = downloadSource.join('/');
        }

        axios
          .post(
            `${(import.meta as any)?.env?.VITE_DOCUMENT_STORE_BASE_URL || (window as any).VITE_DOCUMENT_STORE_BASE_URL || 'https://stock.cmcts.ai/c-agent/s3e'}/api/download`,
            {
              paths: [downloadSource],
            },
            {
              responseType: 'arraybuffer',
            },
          )
          .then(async (response) => {
            console.log('response', response.data);

            try {
              const loadingTask = pdfjs.getDocument({ data: response.data });
              const pdf = await loadingTask.promise;
              setPdfDoc(pdf);
            } catch (err) {
              const error = err as Error;
              setError('Failed to load PDF: ' + error.message);
            }
          })
          .catch((err) => {
            const error = err as Error;
            setError('Failed to download PDF: ' + error.message);
          })
          .finally(() => {
            setIsLoading(false);
          });

        return;
      } else if (props.value?.metadata?.source?.endsWith('.docx') || props.value?.metadata?.source?.endsWith('.xlsx')) {
        setIsLoading(true);
        setError(null);

        let downloadSource: any = props.value.metadata.source || '';
        if (downloadSource.startsWith('s3://')) {
          downloadSource = downloadSource.replace('s3://', '');
          downloadSource = downloadSource.split('/') as string[];
          downloadSource.shift();
          downloadSource = downloadSource.join('/');
        }

        try {
          await axios
            .post(
              `${(import.meta as any)?.env?.VITE_DOCUMENT_STORE_BASE_URL || (window as any).VITE_DOCUMENT_STORE_BASE_URL || 'https://vib.cagent.cmcts.ai/s3-explorer'}/api/download`,
              {
                paths: [downloadSource],
              },
              {
                responseType: 'blob',
              },
            )
            .then(async (response) => {
              const url = window.URL.createObjectURL(response.data);

              // Create a temporary link element
              const link = document.createElement('a');
              link.href = url;

              link.setAttribute('download', props.value?.metadata?.source.split('/').pop());
              document.body.appendChild(link);
              link.click();

              // Clean up
              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);
            });
        } catch (error) {
          setError('Failed to download PDF: ' + (error as unknown as Error).message);
        } finally {
          setIsLoading(false);
          toggleBot();
        }
        return;
      }

      if (typeof props.value?.pageContent === 'string') {
        // Render markdown content
        preEl.innerHTML = Marked.parse(props.value.pageContent);
        preEl.style.padding = '1rem';
        preEl.style.lineHeight = '1.25rem';

        // Make external links open in new tab
        preEl.querySelectorAll('a').forEach((link) => {
          link.target = '_blank';
        });
      } else {
        preEl.innerHTML = syntaxHighlight(JSON.stringify(props?.value, undefined, 2));
      }
    }
  });

  onCleanup(() => {
    if (pdfDoc()) {
      pdfDoc()?.destroy();
      setPdfDoc(null);
    }
    // Clean up canvas references
    canvasRefs = [];
  });

  const [isBotOpened, setIsBotOpened] = createSignal(
    // eslint-disable-next-line solid/reactivity
    popupProps.isOpen ?? false,
  );

  createEffect(() => {
    if (isNotDefined(props.isOpen) || props.isOpen === isBotOpened()) return;
    toggleBot();
  });

  const stopPropagation = (event: MouseEvent) => {
    event.stopPropagation();
  };

  const openBot = () => {
    setIsBotOpened(true);
    popupProps.onOpen?.();
    document.body.style.overflow = 'hidden';
  };

  const closeBot = () => {
    setIsBotOpened(false);
    popupProps.onClose?.();
    document.body.style.overflow = 'auto';
  };

  const toggleBot = () => {
    isBotOpened() ? closeBot() : openBot();
  };

  return (
    <Show when={isBotOpened()}>
      <style>{styles}</style>
      <div class="relative z-10" aria-labelledby="modal-title" role="dialog" aria-modal="true" style={{ 'z-index': 1100 }} on:click={closeBot}>
        <style>{styles}</style>
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity animate-fade-in" />
        <div class="fixed inset-0 z-10 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center text-center sm:p-0">
            <div
              class={`relative transform overflow-hidden rounded-lg text-left shadow-xl transition-all sm:my-8 sm:w-full ${props.value?.metadata?.source?.endsWith('.pdf') ? 'sm:max-w-3xl' : 'sm:max-w-xl'}`}
              style={{
                'background-color': 'transparent',
                'margin-left': '20px',
                'margin-right': '20px',
              }}
              on:click={stopPropagation}
              on:pointerdown={stopPropagation}
            >
              {props.value && (
                <div style={{ background: 'white', margin: 'auto', padding: '7px' }}>
                  <Show when={isLoading()}>
                    <div class="flex justify-center items-center p-4">
                      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
                      <span class="ml-2">Loading{props.value?.metadata?.source?.endsWith('.pdf') ? ' PDF ' : ''}...</span>
                    </div>
                  </Show>
                  <Show when={error()}>
                    <div class="text-red-500 p-4">{error()}</div>
                  </Show>
                  <div
                    style={{
                      'font-family': 'inherit',
                      'font-size': '0.875rem',
                    }}
                    ref={preEl}
                    class="m-0 p-0 prose prose-sm max-w-none"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Show>
  );
};
