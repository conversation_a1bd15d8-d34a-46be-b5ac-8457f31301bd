import { JSX } from 'solid-js/jsx-runtime';

const defaultButtonColor = '#E74864';

export const ThumbsDownIcon = (props: JSX.SvgSVGAttributes<SVGSVGElement> & { filled?: boolean }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    class={`icon icon-tabler icon-tabler-thumbs-down w-6 h-6 ${props.class || ''}`}
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill={props.filled ? (props.color ?? defaultButtonColor) : 'none'}
    stroke={props.color ?? defaultButtonColor}
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
    {...props}
  >
    <path d="M17 14V2" />
    <path d="M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z" />
  </svg>
);
