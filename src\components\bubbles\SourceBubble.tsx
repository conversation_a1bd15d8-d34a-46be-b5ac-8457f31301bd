type Props = {
  pageContent: string;
  metadata: object;
  onSourceClick?: () => void;
  src?: any;
};

export const SourceBubble = (props: Props) => {
  const isFile =
    props.src?.metadata?.source?.toLowerCase().endsWith('.pdf') ||
    props.src?.metadata?.source?.toLowerCase().endsWith('.txt') ||
    props.src?.metadata?.source?.toLowerCase().endsWith('.html') ||
    props.src?.metadata?.source?.toLowerCase().endsWith('.xlsx') ||
    props.src?.metadata?.source?.toLowerCase().endsWith('.docx') ||
    props.src?.metadata?.source?.toLowerCase().endsWith('.doc') ||
    props.src?.metadata?.source?.toLowerCase().endsWith('.md');

  const label = (() => {
    if (isFile) {
      const paths = props.src.metadata.source.split(/[/\\]/);

      return [paths.pop(), paths.pop()].reverse().filter(Boolean).join('/');
    }

    return props.pageContent;
  })();

  return (
    <>
      <div
        data-modal-target="defaultModal"
        data-modal-toggle="defaultModal"
        class="flex justify-start mb-2 items-start host-container hover:brightness-90 active:brightness-75"
        onClick={() => {
          props.onSourceClick?.();
        }}
      >
        <span
          class="px-2 py-1 ml-1 whitespace-pre-wrap max-w-full chatbot-host-bubble"
          data-testid="host-bubble"
          style={{
            width: 'max-content',
            // 'max-width': isPdfFile ? undefined : '120px',
            'font-size': '13px',
            'border-radius': '15px',
            cursor: 'pointer',
            'text-overflow': 'ellipsis',
            overflow: 'hidden',
            'white-space': 'nowrap',
          }}
        >
          {label}
        </span>
      </div>
    </>
  );
};
