import { createSignal, onMount, Show } from 'solid-js';

type Props = {
  message: string;
  type?: 'error' | 'success' | 'info';
  duration?: number;
  onClose?: () => void;
  style?: Record<string, string>;
};

export const Toast = (props: Props) => {
  const [isVisible, setIsVisible] = createSignal(true);
  
  onMount(() => {
    const duration = props.duration || 5000;
    setTimeout(() => {
      setIsVisible(false);
      props.onClose?.();
    }, duration);
  });

  const getTypeStyles = () => {
    switch (props.type) {
      case 'error':
        return {
          'background-color': '#fee2e2',
          'border-color': '#fca5a5',
          color: '#dc2626',
        };
      case 'success':
        return {
          'background-color': '#dcfce7',
          'border-color': '#86efac',
          color: '#16a34a',
        };
      case 'info':
      default:
        return {
          'background-color': '#dbeafe',
          'border-color': '#93c5fd',
          color: '#2563eb',
        };
    }
  };

  return (
    <Show when={isVisible()}>
      <div
        class="fixed top-4 right-4 z-50 p-4 rounded-lg border shadow-lg max-w-sm"
        style={{
          ...getTypeStyles(),
          ...props.style,
        }}
      >
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">{props.message}</span>
          <button
            class="ml-4 text-current opacity-70 hover:opacity-100"
            onClick={() => {
              setIsVisible(false);
              props.onClose?.();
            }}
          >
            ×
          </button>
        </div>
      </div>
    </Show>
  );
};
