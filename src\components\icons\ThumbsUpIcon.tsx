import { JSX } from 'solid-js/jsx-runtime';

const defaultButtonColor = '#3B81F6';

export const ThumbsUpIcon = (props: JSX.SvgSVGAttributes<SVGSVGElement> & { filled?: boolean }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    class={`icon icon-tabler icon-tabler-thumbs-up w-6 h-6 ${props.class || ''}`}
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill={props.filled ? (props.color ?? defaultButtonColor) : 'none'}
    stroke={props.color ?? defaultButtonColor}
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
    {...props}
  >
    <path d="M7 10v12" />
    <path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z" />
  </svg>
);
