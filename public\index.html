<!--
  NOTE: This .html file (and public folder) is optional and can be safely deleted.
  It serves only as a demo/test page.
  
  The chatbot can be embedded directly on any allowed domain using the embed script that
  is generated when starting the server.
-->

<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Flowise Chatbot Widget</title>
</head>

<body>
  <div id="root">
    <!-- <flowise-fullchatbot></flowise-fullchatbot> -->
  </div>
  <script type="module">
    import Chatbot from './web.js';

    const customStringify = (obj) => {
      let stringified = JSON.stringify(obj, null, 4)
        .replace(/"([^"]+)":/g, '$1:')
        .replace(/: "([^"]+)"/g, (match, value) => (value.includes('<') ? `: "${value}"` : `: '${value}'`))
        .replace(/: "(true|false|\d+)"/g, ': $1')
        .replace(/customCSS: ""/g, 'customCSS: ``');
      return stringified
        .split('\n')
        .map((line, index) => {
          if (index === 0) return line;
          return ' '.repeat(8) + line;
        })
        .join('\n');
    };

    // Example initialization:
    // If your .env contains:
    // agent1=xyz789-uvw456,https://example.com
    // support=abc123-def456,https://example.com
    // salesbot=ghi123-jkl456,https://example.com
    //
    // Then use the environment variable name as chatflowid:
    Chatbot.init({
      // chatflowid: '6a63bb88-88f7-4c4d-805e-9c027c85707b', // or 'support', 'salesbot', etc.
      // chatflowid: 'b573e66d-0e28-4798-8e4f-fbbaff8e236f', // or 'support', 'salesbot', etc.
      // chatflowid: '7ee803b4-e3f8-4f33-bf85-ef9ab9d6f693', // or 'support', 'salesbot', etc.
      chatflowid: '1755164d-cb72-4325-9831-83b295ce42bc', // or 'support', 'salesbot', etc.
      apiHost: 'https://vib.cagent.cmcts.ai',
      // apiHost: 'https://c-agent.cmcts.studio.ai.vn',
      chatwootUrl: 'https://livechat.cagent.cmcts.ai',
      roomIds: {
        '0123456789': 'Xa6HmhfbU5K73ZhHnspzKhqs',
      },
      // theme: customStringify({
      //   button: {
      //     backgroundColor: '#3B81F6',
      //     right: 20,
      //     bottom: 20,
      //     size: 48,
      //     dragAndDrop: true,
      //     iconColor: 'white',
      //     customIconSrc: 'https://raw.githubusercontent.com/walkxcode/dashboard-icons/main/svg/google-messages.svg',
      //     autoWindowOpen: {
      //       autoOpen: true,
      //       openDelay: 2,
      //       autoOpenOnMobile: false,
      //     },
      //   },
      //   tooltip: {
      //     showTooltip: true,
      //     tooltipMessage: 'Hi There 👋!',
      //     tooltipBackgroundColor: 'black',
      //     tooltipTextColor: 'white',
      //     tooltipFontSize: 16,
      //   },
      //   disclaimer: {
      //     title: 'Disclaimer',
      //     message: 'By using this chatbot, you agree to the <a target="_blank" href="https://flowiseai.com/terms">Terms & Condition</a>',
      //     textColor: 'black',
      //     buttonColor: '#3b82f6',
      //     buttonText: 'Start Chatting',
      //     buttonTextColor: 'white',
      //     blurredBackgroundColor: 'rgba(0, 0, 0, 0.4)',
      //     backgroundColor: 'white',
      //   },
      //   customCSS: ``,
      //   chatWindow: {
      //     showTitle: true,
      //     showAgentMessages: true,
      //     title: 'Agent Studio Bot',
      //     titleAvatarSrc: 'https://raw.githubusercontent.com/walkxcode/dashboard-icons/main/svg/google-messages.svg',
      //     welcomeMessage: 'Hello! This is custom welcome message',
      //     errorMessage: 'This is a custom error message',
      //     backgroundColor: '#ffffff',
      //     backgroundImage: 'enter image path or link',
      //     height: 700,
      //     width: 400,
      //     fontSize: 16,
      //     starterPrompts: ['What is a bot?', 'Who are you?'],
      //     starterPromptFontSize: 15,
      //     clearChatOnReload: false,
      //     sourceDocsTitle: 'Sources:',
      //     renderHTML: true,
      //     botMessage: {
      //       backgroundColor: '#f7f8ff',
      //       textColor: '#303235',
      //       showAvatar: true,
      //       avatarSrc: 'https://raw.githubusercontent.com/zahidkhawaja/langchain-chat-nextjs/main/public/parroticon.png',
      //     },
      //     userMessage: {
      //       backgroundColor: '#3B81F6',
      //       textColor: '#ffffff',
      //       showAvatar: true,
      //       avatarSrc: 'https://raw.githubusercontent.com/zahidkhawaja/langchain-chat-nextjs/main/public/usericon.png',
      //     },
      //     textInput: {
      //       placeholder: 'Type your question',
      //       backgroundColor: '#ffffff',
      //       textColor: '#303235',
      //       sendButtonColor: '#3B81F6',
      //       maxChars: 50,
      //       maxCharsWarningMessage: 'You exceeded the characters limit. Please input less than 50 characters.',
      //       autoFocus: true,
      //       sendMessageSound: true,
      //       sendSoundLocation: 'send_message.mp3',
      //       receiveMessageSound: true,
      //       receiveSoundLocation: 'receive_message.mp3',
      //     },
      //     feedback: {
      //       color: '#303235',
      //     },
      //     dateTimeToggle: {
      //       date: true,
      //       time: true,
      //     },
      //     footer: {
      //       textColor: '#303235',
      //       text: 'Powered by',
      //       company: 'Agent Studio',
      //       // companyLink: 'https://flowiseai.com'
      //       companyLink: '#',
      //     },
      //   },
      // }),
    });
  </script>
</body>

</html>